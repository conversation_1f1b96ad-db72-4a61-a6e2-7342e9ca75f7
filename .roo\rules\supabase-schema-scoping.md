# Supabase Schema Scoping

When making Supabase requests, it is crucial to scope your queries to the correct schema to ensure data isolation and proper access control. This is achieved using the `.schema()` method on your Supabase client instance.

## How to Scope a Request

To scope a Supabase request, call the `.schema()` method immediately after your `supabase` client instance and pass the name of the desired schema as a string argument.

```typescript
const { data, error } = await supabase
  .schema("your_schema_name") // Replace "your_schema_name" with the actual schema you want to use
  .from("your_table_name")
  .select("*");

if (error) {
  console.error("Error fetching data:", error.message);
} else {
  console.log("Fetched data:", data);
}
```

## Examples from the Codebase

Here are examples demonstrating schema scoping within our project:

**Example 1: Updating an image in the `app_media` schema**

```typescript
const updating = await supabase
    .schema("app_media")
    .from("image")
    .update({ base64_placeholder: base64 })
    .eq("object_id", validation.data.object_id);
```

**Example 2: Fetching currency data from the `app_transaction` schema**

```typescript
const currencies = await supabase
    .schema("app_transaction")
    .from("currency")
    .select("code, units_per_soda, config(base_currency)");
```

**Example 3: Calling an RPC function in the `app_transaction` schema**

```typescript
const { data, error } = await supabase
  .schema("app_transaction")
  .rpc("submit_order", {
    p_receiver_id: "some_uuid",
    p_soda_amount: 100,
  });

if (error) {
  console.error("Error submitting order:", error.message);
} else {
  console.log("Order submitted:", data);
}
```

## Important Considerations

*   **Schema Exposure:** The schema you are attempting to access must be explicitly exposed in your Supabase project settings. If a schema is not exposed, you will not be able to query it, even with the `.schema()` method.
*   **Data Isolation:** Using schemas helps in logically separating data within your Supabase project, which is vital for multi-tenant applications or organizing different modules of your application.
*   **Security:** Proper schema scoping, combined with Row Level Security (RLS), enhances the security of your data by ensuring that users can only access data within their permitted schemas and tables.