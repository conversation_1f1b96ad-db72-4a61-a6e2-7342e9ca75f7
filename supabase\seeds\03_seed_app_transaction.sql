--------------------------------------------------------------------------------
-- Table of Contents
--------------------------------------------------------------------------------
-- 1. Test Deposit
-- 2. Test Wallet
-- 3. Test Withdrawal Request
--------------------------------------------------------------------------------
-- 1. Test Deposit
--------------------------------------------------------------------------------
-- This deposit should result in:
-- primary_balance = 20 (200 / 10)
-- secondary_balance = 0 (200 % 10)
--------------------------------------------------------------------------------
-- 2. Test Wallet
--------------------------------------------------------------------------------
INSERT INTO
  app_transaction.wallet (user_id, soda_balance, cap_balance)
VALUES
  ('c7702895-b2d6-472b-b026-51e19ebbc97c', 2000, 0) ON CONFLICT (user_id)
DO
UPDATE
SET
  soda_balance = EXCLUDED.soda_balance,
  cap_balance = EXCLUDED.cap_balance
;

--------------------------------------------------------------------------------
-- 3. Test Withdrawal Request
--------------------------------------------------------------------------------
INSERT INTO
  app_transaction.withdrawal_request (user_id, soda_amount, currency, status)
VALUES
  ('c7702895-b2d6-472b-b026-51e19ebbc97c', 200, 'TRY', 'pending') ON CONFLICT (user_id)
DO
UPDATE
SET
  soda_amount = EXCLUDED.soda_amount,
  currency = EXCLUDED.currency,
  status = EXCLUDED.status
;