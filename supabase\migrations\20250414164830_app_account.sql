--------------------------------------------------------------------------------
-- Table of Contents
--------------------------------------------------------------------------------
-- 1. Schema
--    1.1. Schema Cleanup
--    1.2. Schema Setup
-- 2. Enum Types
--    2.1. KYC_STATUS Enum
--    2.2. AVAILABILITY_TYPE Enum
-- 3. Helper Functions
--    3.1. check_availability_overlap Function
-- 4. Profile
--    4.1. Profile Table Definition
--    4.2. Profile RLS Policies
--    4.3. Profile Triggers
-- 5. Locale
--    5.1. Locale Table Definition
--    5.2. Locale RLS Policies
-- 6. KYC
--    6.1. KYC Table Definition
--    6.2. KYC RLS Policies
-- 7. IBAN
--    7.1. IBAN Table Definition
--    7.2. IBAN RLS Policies
-- 8. Privacy
--    8.1. Privacy Table Definition
--    8.2. Privacy RLS Policies
-- 9. Availability
--    9.1. Availability Table Definition
--    9.2. Availability RLS Policies
--    9.3. Availability Triggers
--------------------------------------------------------------------------------
-- 1. Schema
--------------------------------------------------------------------------------
-- 1.1. Schema Cleanup
--------------------------------------------------------------------------------
DROP SCHEMA IF EXISTS app_account CASCADE
;

--------------------------------------------------------------------------------
-- 1.2. Schema Setup
--------------------------------------------------------------------------------
CREATE SCHEMA IF NOT EXISTS app_account
;

GRANT USAGE ON SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_account TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_account
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

--------------------------------------------------------------------------------
-- 2. Enum Types
--------------------------------------------------------------------------------
-- 2.1. KYC_STATUS Enum
--------------------------------------------------------------------------------
CREATE TYPE app_account.KYC_STATUS AS ENUM('draft', 'pending', 'approved', 'rejected')
;

--------------------------------------------------------------------------------
-- 2.2. AVAILABILITY_TYPE Enum
--------------------------------------------------------------------------------
CREATE TYPE app_account.AVAILABILITY_TYPE AS ENUM('individual_day', 'weekdays', 'weekends')
;

--------------------------------------------------------------------------------
-- 3. Helper Functions
--------------------------------------------------------------------------------
-- 3.1. check_availability_overlap Function
--------------------------------------------------------------------------------
CREATE
OR REPLACE FUNCTION app_account.check_availability_overlap () RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
  -- Check for overlapping availability for the same user
  IF EXISTS (
    SELECT 1
    FROM app_account.availability existing
    WHERE
      existing.user_id = NEW.user_id
      AND existing.id != NEW.id -- Exclude the current row during update
      AND (
        -- Check for overlap between NEW and existing individual_day
        (NEW.availability_type = 'individual_day' AND 
         existing.availability_type = 'individual_day' AND 
         NEW.day_of_week = existing.day_of_week)
        
        -- Check for overlap between NEW individual_day and existing weekdays
        OR (NEW.availability_type = 'individual_day' AND 
            existing.availability_type = 'weekdays' AND 
            NEW.day_of_week IN ('monday', 'tuesday', 'wednesday', 'thursday', 'friday'))
        
        -- Check for overlap between NEW individual_day and existing weekends
        OR (NEW.availability_type = 'individual_day' AND 
            existing.availability_type = 'weekends' AND 
            NEW.day_of_week IN ('sunday', 'saturday'))
        
        -- Check for overlap between NEW weekdays and existing individual_day
        OR (NEW.availability_type = 'weekdays' AND 
            existing.availability_type = 'individual_day' AND 
            existing.day_of_week IN ('monday', 'tuesday', 'wednesday', 'thursday', 'friday'))
        
        -- Check for overlap between NEW weekdays and existing weekdays
        OR (NEW.availability_type = 'weekdays' AND 
            existing.availability_type = 'weekdays')
        
        -- Check for overlap between NEW weekends and existing individual_day
        OR (NEW.availability_type = 'weekends' AND 
            existing.availability_type = 'individual_day' AND 
            existing.day_of_week IN ('sunday', 'saturday'))
        
        -- Check for overlap between NEW weekends and existing weekends
        OR (NEW.availability_type = 'weekends' AND 
            existing.availability_type = 'weekends')
      )
      -- Check if time ranges overlap
      AND NEW.start_time < existing.end_time
      AND existing.start_time < NEW.end_time
  ) THEN
    RAISE EXCEPTION 'Availability entry overlaps with an existing entry.';
  END IF;

  RETURN NEW;
END;
$$
;

--------------------------------------------------------------------------------
-- 4. Profile
--------------------------------------------------------------------------------
-- 4.1. Profile Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
  app_account.profile (
    user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
    NAME TEXT NOT NULL CHECK (
      LENGTH(NAME) <= 10
      AND NAME ~ '^[a-zA-Z0-9_-]+(?: [a-zA-Z0-9_-]+)*$'
    ),
    honorific honorific,
    gender gender,
    join_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    birth_date DATE,
    bio JSONB NULL CHECK (public.is_valid_locale_jsonb (bio)),
    CHECK (birth_date <= NOW() - INTERVAL '18 years')
  )
;

--------------------------------------------------------------------------------
-- 4.2. Profile RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_account.profile ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "profile_select_all" ON app_account.profile FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "profile_insert_own" ON app_account.profile FOR INSERT
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
;

CREATE POLICY "profile_update_own" ON app_account.profile FOR
UPDATE USING (
  user_id = (
    SELECT
      auth.uid ()
  )
)
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
;

CREATE POLICY "profile_insert_admin" ON app_account.profile FOR INSERT
WITH
  CHECK (
    (
      SELECT
        app_role.is_admin ()
    )
  )
;

CREATE POLICY "profile_update_admin" ON app_account.profile FOR
UPDATE USING (
  (
    SELECT
      app_role.is_admin ()
  )
)
;

CREATE POLICY "profile_delete_admin" ON app_account.profile FOR DELETE USING (
  (
    SELECT
      app_role.is_admin ()
  )
)
;

--------------------------------------------------------------------------------
-- 4.3. Profile Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER profile_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_account.profile FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('bio')
;

--------------------------------------------------------------------------------
-- 5. Locale
--------------------------------------------------------------------------------
-- 5.1. Locale Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
  app_account.locale (
    user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
    locale locale,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    PRIMARY KEY (user_id, locale)
  )
;

--------------------------------------------------------------------------------
-- 5.2. Locale RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_account.locale ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "locale_select_own_or_admin" ON app_account.locale FOR
SELECT
  USING (
    user_id = (
      SELECT
        auth.uid ()
    )
    OR (
      SELECT
        app_role.is_admin ()
    )
  )
;

CREATE POLICY "locale_insert_own" ON app_account.locale FOR INSERT
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
;

CREATE POLICY "locale_update_own" ON app_account.locale FOR
UPDATE USING (
  user_id = (
    SELECT
      auth.uid ()
  )
)
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
  )
;

CREATE POLICY "locale_delete_own" ON app_account.locale FOR DELETE USING (
  user_id = (
    SELECT
      auth.uid ()
  )
)
;

CREATE POLICY "locale_insert_admin" ON app_account.locale FOR INSERT
WITH
  CHECK (
    (
      SELECT
        app_role.is_admin ()
    )
  )
;

CREATE POLICY "locale_update_admin" ON app_account.locale FOR
UPDATE USING (
  (
    SELECT
      app_role.is_admin ()
  )
)
;

CREATE POLICY "locale_delete_admin" ON app_account.locale FOR DELETE USING (
  (
    SELECT
      app_role.is_admin ()
  )
)
;

--------------------------------------------------------------------------------
-- 6. KYC
--------------------------------------------------------------------------------
-- 6.1. KYC Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
  app_account.kyc (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    user_id UUID UNIQUE REFERENCES auth.users (id) ON DELETE CASCADE,
    status app_account.KYC_STATUS NOT NULL DEFAULT 'pending',
    full_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  )
;

--------------------------------------------------------------------------------
-- 6.2. KYC RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_account.kyc ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "kyc_select_own_draft_pending_or_admin" ON app_account.kyc FOR
SELECT
  USING (
    user_id = (
      SELECT
        auth.uid ()
    )
    AND (
      status = 'draft'
      OR status = 'pending'
    )
    OR (
      SELECT
        app_role.is_admin ()
    )
  )
;

CREATE POLICY "kyc_insert_own_draft_pending" ON app_account.kyc FOR INSERT
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
    AND (
      status = 'draft'
      OR status = 'pending'
    )
  )
;

CREATE POLICY "kyc_update_own_draft_pending" ON app_account.kyc FOR
UPDATE USING (
  user_id = (
    SELECT
      auth.uid ()
  )
)
WITH
  CHECK (
    user_id = (
      SELECT
        auth.uid ()
    )
    AND (
      status = 'draft'
      OR status = 'pending'
    )
  )
;

CREATE POLICY "kyc_delete_own_draft_pending" ON app_account.kyc FOR DELETE USING (
  user_id = (
    SELECT
      auth.uid ()
  )
  AND (
    status = 'draft'
    OR status = 'pending'
  )
)
;

--------------------------------------------------------------------------------
-- 7. IBAN
--------------------------------------------------------------------------------
-- 7.1. IBAN Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
  app_account.iban (
    user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
    iban TEXT NOT NULL CHECK (iban ~ '^[A-Z]{2}[0-9]{2}[A-Z0-9]{11,30}$'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  )
;

--------------------------------------------------------------------------------
-- 7.2. IBAN RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_account.iban ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "iban_select_own" ON app_account.iban FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "iban_insert_own" ON app_account.iban FOR INSERT
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "iban_update_own" ON app_account.iban FOR
UPDATE USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
)
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "iban_delete_own" ON app_account.iban FOR DELETE USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
)
;

--------------------------------------------------------------------------------
-- 8. Privacy
--------------------------------------------------------------------------------
-- 8.1. Privacy Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
  app_account.privacy (
    user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
    show_profile BOOLEAN NOT NULL DEFAULT TRUE,
    show_activity BOOLEAN NOT NULL DEFAULT TRUE,
    show_age BOOLEAN NOT NULL DEFAULT TRUE,
    show_gender BOOLEAN NOT NULL DEFAULT TRUE,
    show_in_leaderboard BOOLEAN NOT NULL DEFAULT TRUE
  )
;

--------------------------------------------------------------------------------
-- 8.2. Privacy RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_account.privacy ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "privacy_select_own" ON app_account.privacy FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "privacy_insert_own" ON app_account.privacy FOR INSERT
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "privacy_update_own" ON app_account.privacy FOR
UPDATE USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
)
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

--------------------------------------------------------------------------------
-- 9. Availability
--------------------------------------------------------------------------------
-- 9.1. Availability Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
  app_account.availability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
    start_time TIME WITH TIME ZONE NOT NULL,
    end_time TIME WITH TIME ZONE NOT NULL,
    availability_type app_account.AVAILABILITY_TYPE NOT NULL,
    day_of_week public.DAY_OF_WEEK,
    CONSTRAINT start_time_before_end_time CHECK (start_time < end_time),
    CONSTRAINT min_duration_15_minutes CHECK (
      ('2000-01-01'::DATE + end_time) - ('2000-01-01'::DATE + start_time) >= INTERVAL '15 minutes'
    ),
    CONSTRAINT day_of_week_assignable CHECK (
      (
        availability_type = 'individual_day'
        AND day_of_week IS NOT NULL
      )
      OR (
        availability_type != 'individual_day'
        AND day_of_week IS NULL
      )
    )
  )
;

--------------------------------------------------------------------------------
-- 9.2. Availability RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_account.availability ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "availability_select_own" ON app_account.availability FOR
SELECT
  USING (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "availability_insert_own" ON app_account.availability FOR INSERT
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "availability_update_own" ON app_account.availability FOR
UPDATE USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
)
WITH
  CHECK (
    (
      SELECT
        auth.uid ()
    ) = user_id
  )
;

CREATE POLICY "availability_delete_own" ON app_account.availability FOR DELETE USING (
  (
    SELECT
      auth.uid ()
  ) = user_id
)
;

CREATE POLICY "availability_insert_admin" ON app_account.availability FOR INSERT
WITH
  CHECK (
    (
      SELECT
        app_role.is_admin ()
    )
  )
;

CREATE POLICY "availability_update_admin" ON app_account.availability FOR
UPDATE USING (
  (
    SELECT
      app_role.is_admin ()
  )
)
;

CREATE POLICY "availability_delete_admin" ON app_account.availability FOR DELETE USING (
  (
    SELECT
      app_role.is_admin ()
  )
)
;

--------------------------------------------------------------------------------
-- 9.3. Availability Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER availability_check_overlap BEFORE INSERT
OR
UPDATE ON app_account.availability FOR EACH ROW
EXECUTE FUNCTION app_account.check_availability_overlap ()
;