import { test, expect, beforeAll, afterAll, describe } from "vitest";
import { userClient, serviceClient, user } from "./setup";
import { readFileSync } from "fs";
import { fileTypeFromBuffer } from "file-type";

const filePath = "tests/supabase/objects/test-avatar.jpg";

describe("Storage RLS policies (user-id)", () => {
  test("Can insert", async () => {
    const file = readFileSync(filePath);
    const fileType = await fileTypeFromBuffer(file);

    const insertResponse = await userClient.storage
      .from("avatar")
      .upload(user.id, file, {
        upsert: true,
        metadata: {
          description: "insert test"
        },
        contentType: fileType?.mime
      });

    expect(insertResponse.error).toBeNull();

    const insertCheckResponse = await userClient.storage
      .from("avatar")
      .info(user.id);

    expect(insertCheckResponse.data?.metadata?.description).toBe("insert test");
  });

  test("Can update", async () => {
    const file = readFileSync(filePath);
    const fileType = await fileTypeFromBuffer(file);

    const updateResponse = await userClient.storage
      .from("avatar")
      .upload(user.id, file, {
        upsert: true,
        metadata: {
          description: "update test"
        },
        contentType: fileType?.mime
      });

    expect(updateResponse.error).toBeNull();

    const updateCheckResponse = await userClient.storage
      .from("avatar")
      .info(user.id);

    expect(updateCheckResponse.data?.metadata?.description).toBe("update test");
  });

  test("Can delete", async () => {
    await userClient.storage.from("avatar").remove([user.id]);

    const deleteCheckResponse = await userClient.storage
      .from("avatar")
      .info(user.id);

    expect(deleteCheckResponse.data?.name).toBeUndefined();
  });
});

describe("Storage RLS policies (non-user-id)", () => {
  beforeAll(async () => {
    const file = readFileSync(filePath);
    const contentType = (await fileTypeFromBuffer(file))?.mime;

    const { error } = await serviceClient.storage
      .from("avatar")
      .upload("not-user-id", file, { upsert: true, contentType });

    if (error) throw error;
  });

  afterAll(async () => {
    const { error } = await serviceClient.storage
      .from("avatar")
      .remove(["not-user-id"]);

    if (error) throw error;
  });

  test("Cannot insert", async () => {
    const file = readFileSync(filePath);
    const contentType = (await fileTypeFromBuffer(file))?.mime;

    const insertResponse = await userClient.storage
      .from("avatar")
      .upload("not-user-id-insert", file, { contentType });

    expect(insertResponse.error).not.toBeNull();

    const insertCheckResponse = await userClient.storage
      .from("avatar")
      .info("not-user-id-insert");

    expect(insertCheckResponse.data?.name).toBeUndefined();
  });

  test("Cannot update", async () => {
    const file = readFileSync(filePath);
    const contentType = (await fileTypeFromBuffer(file))?.mime;

    const updateResponse = await userClient.storage
      .from("avatar")
      .upload("not-user-id", file, { contentType });

    expect(updateResponse.error).not.toBeNull();
  });

  test("Cannot delete", async () => {
    await userClient.storage.from("avatar").remove(["not-user-id"]);

    const deleteCheckResponse = await userClient.storage
      .from("avatar")
      .info("not-user-id");

    expect(deleteCheckResponse.data?.name).toBe("not-user-id");
  });
});
