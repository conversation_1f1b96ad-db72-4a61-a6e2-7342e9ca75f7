```sql
--------------------------------------------------------------------------------
-- Table of Contents
--------------------------------------------------------------------------------
-- 1. Schema
--    1.1. Schema Cleanup
--    1.2. Schema Setup
-- 2. Types (skip if none)
--    2.1. Enum Type One (repeat for each enum type)
--    2.3. Domain Type One (skip if none)
-- 3. Helper Functions  (skip if none)
--    3.1. Function One (repeat for each function)
-- 4. Table One (repeat for each table, do not forget to rename `Table One`)
--    4.1. Table One Table Definition
--    4.2. Table One RLS Policies
--    4.3. Table One Rows (skip if none)
--    4.4. Table One Triggers (skip if none)
--    4.5. Table One Indexes (skip if none)
-- 5. Data Migrations (skip if none)
--    5.1. Data Migration One (repeat for each data migration)
--------------------------------------------------------------------------------
-- 1. Schema
--------------------------------------------------------------------------------
-- 1.1. Schema Cleanup
--------------------------------------------------------------------------------
DROP SCHEMA IF EXISTS app_schema CASCADE
;
--------------------------------------------------------------------------------
-- 1.2. Schema Setup
--------------------------------------------------------------------------------
CREATE SCHEMA IF NOT EXISTS app_schema
;

GRANT USAGE ON SCHEMA app_schema TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_schema TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_schema TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_schema TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_schema
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_schema
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_schema
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

--------------------------------------------------------------------------------
-- 2. Enum Types
--------------------------------------------------------------------------------
-- 2.1. Enum Type One
--------------------------------------------------------------------------------
-- CREATE TYPE app_schema.ENUM_TYPE_ONE AS ENUM('value1', 'value2')
-- ;

--------------------------------------------------------------------------------
-- 2.2. Enum Type Two
--------------------------------------------------------------------------------
-- CREATE TYPE app_schema.ENUM_TYPE_TWO AS ENUM('value1', 'value2')
-- ;

--------------------------------------------------------------------------------
-- 3. Helper Functions
--------------------------------------------------------------------------------
-- 3.1. Function One
--------------------------------------------------------------------------------
-- CREATE
-- OR REPLACE FUNCTION app_schema.function_one () RETURNS VOID LANGUAGE plpgsql
-- AS $$
-- BEGIN
--   -- Function logic here
-- END;
-- $$
-- ;

--------------------------------------------------------------------------------
-- 3.2. Function Two
--------------------------------------------------------------------------------
-- CREATE
-- OR REPLACE FUNCTION app_schema.function_two () RETURNS VOID LANGUAGE plpgsql
-- AS $$
-- BEGIN
--   -- Function logic here
-- END;
-- $$
-- ;

--------------------------------------------------------------------------------
-- 4. Table One
--------------------------------------------------------------------------------
-- 4.1. Table One Definition
--------------------------------------------------------------------------------
-- CREATE TABLE
--   app_schema.table_one (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
--     column_one TEXT NOT NULL,
--     column_two TEXT NOT NULL,
--   )
-- ;

--------------------------------------------------------------------------------
-- 4.2. Table One RLS Policies
--------------------------------------------------------------------------------
-- ALTER TABLE app_schema.table_one ENABLE ROW LEVEL SECURITY
-- ;

-- CREATE POLICY "table_one_select_all" ON app_schema.table_one FOR
-- SELECT
--   USING (TRUE)
-- ;

-- CREATE POLICY "table_one_insert_admin" ON app_schema.table_one FOR INSERT
-- WITH
--   CHECK (app_role.is_admin (auth.uid ()))
-- ;

-- CREATE POLICY "table_one_update_admin" ON app_schema.table_one FOR
-- UPDATE USING (app_role.is_admin (auth.uid ()))
-- WITH
--   CHECK (app_role.is_admin (auth.uid ()))
-- ;

-- CREATE POLICY "table_one_delete_admin" ON app_schema.table_one FOR DELETE USING (app_role.is_admin (auth.uid ()))
-- ;

--------------------------------------------------------------------------------
-- 4.3. Table One Triggers (optional)
--------------------------------------------------------------------------------
-- CREATE TRIGGER table_one_validate_columns BEFORE INSERT
-- OR
-- UPDATE ON app_schema.table_one FOR EACH ROW
-- EXECUTE FUNCTION app_schema.check_function()
-- ;

--------------------------------------------------------------------------------
-- 4.4. Table One Indexes (optional)
--------------------------------------------------------------------------------
-- CREATE INDEX IF NOT EXISTS index_name ON app_schema.table_one (column_name)
-- ;
-- CREATE INDEX IF NOT EXISTS index_name ON app_schema.table_one (column_name)
-- ;

--------------------------------------------------------------------------------
-- 5. Data Migrations
--------------------------------------------------------------------------------
-- 5.1. Data Migration One
--------------------------------------------------------------------------------
-- INSERT INTO app_schema.table_name (column1, column2) VALUES ('value1', 'value2')
-- ;

--------------------------------------------------------------------------------
-- 5.2. Data Migration Two
--------------------------------------------------------------------------------
-- UPDATE app_schema.table_name SET column_name = 'new_value' WHERE column_name = 'old_value'
-- ;

```
