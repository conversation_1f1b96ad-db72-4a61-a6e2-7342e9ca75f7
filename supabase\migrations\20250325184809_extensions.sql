--------------------------------------------------------------------------------
-- TABLE OF CONTENTS
--------------------------------------------------------------------------------
-- 1. Cleanup Operations
-- 2. Extensions
--    2.1. pg_cron Extension
--    2.2. pg_net Extension
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 1. CLE<PERSON>UP OPERATIONS
--------------------------------------------------------------------------------
DROP EXTENSION IF EXISTS pg_graphql CASCADE
;

DROP EXTENSION IF EXISTS pg_cron CASCADE
;

DROP EXTENSION IF EXISTS pg_net CASCADE
;

DROP SCHEMA IF EXISTS net CASCADE
;

--------------------------------------------------------------------------------
-- 2. EXTENSIONS
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 2.1. pg_cron Extension
--------------------------------------------------------------------------------
-- Install pg_cron extension
CREATE EXTENSION pg_cron
WITH
  SCHEMA pg_catalog
;

-- Grant usage on schema cron to postgres
GRANT usage ON SCHEMA cron TO postgres
;

-- Grant all privileges on all tables in schema cron to postgres
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA cron TO postgres
;

--------------------------------------------------------------------------------
-- 2.2. pg_net Extension
--------------------------------------------------------------------------------
-- Install pg_net extension
CREATE EXTENSION pg_net
;

--------------------------------------------------------------------------------
-- 3. SECRETS
--------------------------------------------------------------------------------
-- 2.3. API_URL
--------------------------------------------------------------------------------
SELECT
  vault.create_secret ('http://host.docker.internal:3001', 'API_URL', 'API URL for external service')
WHERE
  NOT EXISTS (
    SELECT
      1
    FROM
      vault.secrets
    WHERE
      NAME = 'API_URL'
  )
;

--------------------------------------------------------------------------------
-- 2.4. API_CALL_SECRET
--------------------------------------------------------------------------------
SELECT
  vault.create_secret (
    '4mebHx#2Lf9rjir^^fvaRi53wiK6667!hMH4E5Yg&Km&6RE&eb',
    'API_CALL_SECRET',
    'API Call Secret for external service'
  )
WHERE
  NOT EXISTS (
    SELECT
      1
    FROM
      vault.secrets
    WHERE
      NAME = 'API_CALL_SECRET'
  )
;