--------------------------------------------------------------------------------
-- Table of Contents
--------------------------------------------------------------------------------
-- 1. Schema
--    1.1. Schema Cleanup
--    1.2. Schema Setup
-- 2. Enum Types
--    2.1. FIELD_TYPE Enum
-- 3. Category
--    3.1. Category Table
--    3.2. Category RLS Policies
--    3.3. Category Triggers
-- 4. Activity
--    4.1. Activity Table
--    4.2. Activity RLS Policies
--    4.3. Activity Triggers
-- 5. Tag
--    5.1. Tag Table
--    5.2. Tag RLS Policies
--    5.3. Tag Triggers
-- 6. Activity Tag
--    6.1. Activity Tag Table
--    6.2. Activity Tag RLS Policies
-- 7. Field
--    7.1. Field Table
--    7.2. Field RLS Policies
--    7.3. Field Triggers
-- 8. Category Field
--    8.1. Category Field Table
--    8.2. Category Field RLS Policies
-- 9. Activity Field
--    9.1. Activity Field Table
--    9.2. Activity Field RLS Policies
-- 10. Field Option
--    10.1. Field Option Table
--    10.2. Field Option RLS Policies
--    10.3. Field Option Triggers
-- 11. Category Field Option
--    11.1. Category Field Option Table
--    11.2. Category Field Option RLS Policies
-- 12. Activity Field Option
--    12.1. Activity Field Option Table
--    12.2. Activity Field Option RLS Policies
-- 13. Pricing Unit
--    13.1. Pricing Unit Table
--    13.2. Pricing Unit RLS Policies
--    13.3. Pricing Unit Triggers
-- 14. Service
--    14.1. Service Table
--    14.2. Service RLS Policies
--    14.3. Service Triggers
-- 15. Pricing Unit Service
--    15.1. Pricing Unit Service Table Definition
--    15.2. Pricing Unit Service RLS Policies
-- 16. Activity Service
--    16.1. Activity Service Table Definition
--    16.2. Activity Service RLS Policies
-- 17. Category Service
--    17.1. Category Service Table Definition
--    17.2. Category Service RLS Policies
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 1.1. Schema Cleanup
--------------------------------------------------------------------------------
DROP SCHEMA IF EXISTS app_activity CASCADE
;

--------------------------------------------------------------------------------
-- 1.2. Schema Setup
--------------------------------------------------------------------------------
CREATE SCHEMA IF NOT EXISTS app_activity
;

GRANT USAGE ON SCHEMA app_activity TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_activity TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_activity TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_activity TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_activity
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_activity
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_activity
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

--------------------------------------------------------------------------------
-- 2. Enum Types
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 2.1. FIELD_TYPE Enum
--------------------------------------------------------------------------------
CREATE TYPE app_activity.FIELD_TYPE AS ENUM('custom', 'select', 'multiselect')
;

--------------------------------------------------------------------------------
-- 3. Category
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 3.1. Category Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.category (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
        parent_category_id UUID REFERENCES app_activity.category (id) ON DELETE SET NULL,
        NAME JSONB NOT NULL,
        description JSONB,
        icon TEXT,
        cover TEXT,
        slug TEXT UNIQUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE (NAME)
    )
;

--------------------------------------------------------------------------------
-- 3.2. Category RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.category ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "category_select_all" ON app_activity.category FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "category_insert_admin" ON app_activity.category FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "category_update_admin" ON app_activity.category FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "category_delete_admin" ON app_activity.category FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 3.3 Category Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER category_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_activity.category FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('name', 'description')
;

CREATE TRIGGER category_check_circular_dependency BEFORE INSERT
OR
UPDATE ON app_activity.category FOR EACH ROW
EXECUTE FUNCTION public.check_circular_dependency (
    'app_activity',
    'category',
    'id',
    'parent_category_id',
    'Circular dependency detected in category hierarchy'
)
;

--------------------------------------------------------------------------------
-- 4. Activity
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 4.1. Activity Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.activity (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
        category_id UUID REFERENCES app_activity.category (id) ON DELETE SET NULL,
        NAME JSONB NOT NULL,
        description JSONB,
        color JSONB CHECK (public.is_valid_color_jsonb (color)),
        slug TEXT UNIQUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE (category_id, NAME)
    )
;

--------------------------------------------------------------------------------
-- 4.2. Activity RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.activity ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_select_all" ON app_activity.activity FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "activity_insert_admin" ON app_activity.activity FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "activity_update_admin" ON app_activity.activity FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "activity_delete_admin" ON app_activity.activity FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 4.3. Activity Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER activity_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_activity.activity FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('name', 'description')
;

--------------------------------------------------------------------------------
-- 5. Tag
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 5.1. Tag Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.tag (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
        NAME JSONB NOT NULL,
        description JSONB,
        slug TEXT UNIQUE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
;

--------------------------------------------------------------------------------
-- 5.2. Tag RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.tag ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "tag_select_all" ON app_activity.tag FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "tag_insert_admin" ON app_activity.tag FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "tag_update_admin" ON app_activity.tag FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "tag_delete_admin" ON app_activity.tag FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 5.3. Tag Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER tag_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_activity.tag FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('name', 'description')
;

--------------------------------------------------------------------------------
-- 6. Activity Tag
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 6.1. Activity Tag Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.activity_tag (
        activity_id UUID REFERENCES app_activity.activity (id) ON DELETE SET NULL,
        tag_id UUID REFERENCES app_activity.tag (id) ON DELETE SET NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        PRIMARY KEY (tag_id, activity_id)
    )
;

--------------------------------------------------------------------------------
-- 6.2. Activity Tag RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.activity_tag ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_tag_select_all" ON app_activity.activity_tag FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "activity_tag_insert_admin" ON app_activity.activity_tag FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "activity_tag_update_admin" ON app_activity.activity_tag FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "activity_tag_delete_admin" ON app_activity.activity_tag FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 7. Field
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 7.1. Field Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.field (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
        NAME JSONB NOT NULL,
        description JSONB,
        TYPE app_activity.FIELD_TYPE NOT NULL DEFAULT 'custom',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
;

--------------------------------------------------------------------------------
-- 7.2. Field RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.field ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "field_select_all" ON app_activity.field FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "field_insert_admin" ON app_activity.field FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "field_update_admin" ON app_activity.field FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "field_delete_admin" ON app_activity.field FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 7.3. Field Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER field_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_activity.field FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('name', 'description')
;

--------------------------------------------------------------------------------
-- 8. Category Field
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 8.1. Category Field Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.category_field (
        field_id UUID REFERENCES app_activity.field (id) ON DELETE SET NULL,
        category_id UUID REFERENCES app_activity.category (id) ON DELETE SET NULL,
        PRIMARY KEY (field_id, category_id)
    )
;

--------------------------------------------------------------------------------
-- 8.2. Category Field RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.category_field ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "category_field_select_all" ON app_activity.category_field FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "category_field_insert_admin" ON app_activity.category_field FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "category_field_update_admin" ON app_activity.category_field FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "category_field_delete_admin" ON app_activity.category_field FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 9. Activity Field
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 9.1. Activity Field Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.activity_field (
        field_id UUID REFERENCES app_activity.field (id) ON DELETE SET NULL,
        activity_id UUID REFERENCES app_activity.activity (id) ON DELETE SET NULL,
        PRIMARY KEY (field_id, activity_id)
    )
;

--------------------------------------------------------------------------------
-- 9.2. Activity Field RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.activity_field ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_field_select_all" ON app_activity.activity_field FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "activity_field_insert_admin" ON app_activity.activity_field FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "activity_field_update_admin" ON app_activity.activity_field FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "activity_field_delete_admin" ON app_activity.activity_field FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 10. Field Option
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 10.1. Field Option Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.field_option (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
        field_id UUID REFERENCES app_activity.field (id) ON DELETE CASCADE,
        NAME JSONB NOT NULL,
        description JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
;

--------------------------------------------------------------------------------
-- 10.2. Field Option RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.field_option ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "field_option_select_all" ON app_activity.field_option FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "field_option_insert_admin" ON app_activity.field_option FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "field_option_update_admin" ON app_activity.field_option FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "field_option_delete_admin" ON app_activity.field_option FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 10.3. Field Option Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER field_option_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_activity.field_option FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('name', 'description')
;

--------------------------------------------------------------------------------
-- 11. Category Field Option
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 11.1. Category Field Option Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.category_field_option (
        field_option_id UUID REFERENCES app_activity.field_option (id) ON DELETE SET NULL,
        category_id UUID REFERENCES app_activity.category (id) ON DELETE CASCADE,
        PRIMARY KEY (field_option_id, category_id)
    )
;

--------------------------------------------------------------------------------
-- 11.2. Category Field Option RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.category_field_option ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "category_field_option_select_all" ON app_activity.category_field_option FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "category_field_option_insert_admin" ON app_activity.category_field_option FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "category_field_option_update_admin" ON app_activity.category_field_option FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "category_field_option_delete_admin" ON app_activity.category_field_option FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 12. Activity Field Option
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 12.1. Activity Field Option Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.activity_field_option (
        field_option_id UUID REFERENCES app_activity.field_option (id) ON DELETE CASCADE,
        activity_id UUID REFERENCES app_activity.activity (id) ON DELETE CASCADE,
        PRIMARY KEY (field_option_id, activity_id)
    )
;

--------------------------------------------------------------------------------
-- 12.2. Activity Field Option RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.activity_field_option ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_field_option_select_all" ON app_activity.activity_field_option FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "activity_field_option_insert_admin" ON app_activity.activity_field_option FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "activity_field_option_update_admin" ON app_activity.activity_field_option FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "activity_field_option_delete_admin" ON app_activity.activity_field_option FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 13. Pricing Unit
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 13.1. Pricing Unit Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.pricing_unit (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
        NAME JSONB NOT NULL,
        description JSONB,
        icon TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
;

--------------------------------------------------------------------------------
-- 13.2. Pricing Unit RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.pricing_unit ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "pricing_unit_select_all" ON app_activity.pricing_unit FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "pricing_unit_insert_admin" ON app_activity.pricing_unit FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "pricing_unit_update_admin" ON app_activity.pricing_unit FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "pricing_unit_delete_admin" ON app_activity.pricing_unit FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 13.3. Pricing Unit Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER pricing_unit_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_activity.pricing_unit FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('name', 'description')
;

--------------------------------------------------------------------------------
-- 14. Service
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 14.1. Service Table
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.service (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
        NAME JSONB NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
;

--------------------------------------------------------------------------------
-- 14.2. Service RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "service_select_all" ON app_activity.service FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "service_insert_admin" ON app_activity.service FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "service_update_admin" ON app_activity.service FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "service_delete_admin" ON app_activity.service FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 14.3. Service Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER service_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_activity.service FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('name')
;

--------------------------------------------------------------------------------
-- 15. Pricing Unit Service
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 15.1. Pricing Unit Service Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.pricing_unit_service (
        pricing_unit_id UUID REFERENCES app_activity.pricing_unit (id) ON DELETE CASCADE,
        service_id UUID REFERENCES app_activity.service (id) ON DELETE CASCADE,
        PRIMARY KEY (pricing_unit_id, service_id)
    )
;

--------------------------------------------------------------------------------
-- 15.2. Pricing Unit Service RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.pricing_unit_service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "pricing_unit_service_select_all" ON app_activity.pricing_unit_service FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "pricing_unit_service_insert_admin" ON app_activity.pricing_unit_service FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "pricing_unit_service_update_admin" ON app_activity.pricing_unit_service FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "pricing_unit_service_delete_admin" ON app_activity.pricing_unit_service FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 16. Activity Service
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 16.1. Activity Service Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.activity_service (
        activity_id UUID REFERENCES app_activity.activity (id) ON DELETE CASCADE,
        service_id UUID REFERENCES app_activity.service (id) ON DELETE CASCADE,
        PRIMARY KEY (activity_id, service_id)
    )
;

--------------------------------------------------------------------------------
-- 16.2. Activity Service RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.activity_service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "activity_service_select_all" ON app_activity.activity_service FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "activity_service_insert_admin" ON app_activity.activity_service FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "activity_service_update_admin" ON app_activity.activity_service FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "activity_service_delete_admin" ON app_activity.activity_service FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 17. Category Service
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 17.1. Category Service Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
    app_activity.category_service (
        category_id UUID REFERENCES app_activity.category (id) ON DELETE CASCADE,
        service_id UUID REFERENCES app_activity.service (id) ON DELETE CASCADE,
        PRIMARY KEY (category_id, service_id)
    )
;

--------------------------------------------------------------------------------
-- 17.2. Category Service RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_activity.category_service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "category_service_select_all" ON app_activity.category_service FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "category_service_insert_admin" ON app_activity.category_service FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "category_service_update_admin" ON app_activity.category_service FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "category_service_delete_admin" ON app_activity.category_service FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;