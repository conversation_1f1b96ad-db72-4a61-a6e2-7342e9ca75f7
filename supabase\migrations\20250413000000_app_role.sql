--------------------------------------------------------------------------------
-- Table of Contents
--------------------------------------------------------------------------------
-- 1. Schema
--    1.1. Schema Cleanup
--    1.2. Schema Setup
-- 2. Helper Functions
--    2.1. is_admin function
--    2.2. is_provider function
-- 3. Admin
--    3.1. Admin Table Definition
--    3.2. Admin RLS Policies
-- 4. Celebrity
--    4.1. Celebrity Table Definition
--    4.2. Celebrity RLS Policies
-- 5. Provider
--    5.1. Provider Table Definition
--    5.2. Provider RLS Policies
--------------------------------------------------------------------------------
-- 1. Schema
--------------------------------------------------------------------------------
-- 1.1. Schema Cleanup
--------------------------------------------------------------------------------
DROP SCHEMA IF EXISTS app_role CASCADE
;

--------------------------------------------------------------------------------
-- 1.2. Schema Setup
--------------------------------------------------------------------------------
CREATE SCHEMA IF NOT EXISTS app_role
;

GRANT USAGE ON SCHEMA app_role TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_role TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_role TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_role TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_role
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_role
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_role
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

--------------------------------------------------------------------------------
-- 2. Helper Functions
--------------------------------------------------------------------------------
-- 2.1. is_admin function
--------------------------------------------------------------------------------
CREATE
OR REPLACE FUNCTION app_role.is_admin () RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER -- Use SECURITY DEFINER to allow the function to access app_role.admin table regardless of the calling user's RLS policies on that table.
AS $$
BEGIN
  RETURN EXISTS (SELECT 1 FROM app_role.admin WHERE user_id = (SELECT auth.uid()));
END;
$$
;

--------------------------------------------------------------------------------
-- 2.2. is_provider function
--------------------------------------------------------------------------------
CREATE
OR REPLACE FUNCTION app_role.is_provider () RETURNS BOOLEAN LANGUAGE plpgsql SECURITY DEFINER -- Use SECURITY DEFINER to allow the function to access app_role.provider table regardless of the calling user's RLS policies on that table.
AS $$
BEGIN
  RETURN EXISTS (SELECT 1 FROM app_role.provider WHERE user_id = (SELECT auth.uid()));
END;
$$
;

--------------------------------------------------------------------------------
-- 3. Admin Table
--------------------------------------------------------------------------------
-- 3.1. Admin Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
  app_role.admin (user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE)
;

--------------------------------------------------------------------------------
-- 3.2. Admin RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_role.admin ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "admin_select_all" ON app_role.admin FOR
SELECT
  USING (TRUE)
;

--------------------------------------------------------------------------------
-- 4. Celebrity Table
--------------------------------------------------------------------------------
-- 4.1. Celebrity Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
  app_role.celebrity (
    user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
    -- NAME TEXT NOT NULL CHECK (
    --   LENGTH(NAME) <= 20
    --   AND NAME ~ '^[a-zA-Z0-9_-]+(?: [a-zA-Z0-9_-]+)*$'
    -- ),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  )
;

--------------------------------------------------------------------------------
-- 4.2. Celebrity RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_role.celebrity ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "celebrity_select_all" ON app_role.celebrity FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "celebrity_insert_admin" ON app_role.celebrity FOR INSERT
WITH
  CHECK (
    (
      SELECT
        app_role.is_admin ()
    )
  )
;

CREATE POLICY "celebrity_delete_admin" ON app_role.celebrity FOR DELETE USING (
  (
    SELECT
      app_role.is_admin ()
  )
)
;

CREATE POLICY "celebrity_update_own_or_admin" ON app_role.celebrity FOR
UPDATE USING (
  user_id = (
    SELECT
      auth.uid ()
  )
  OR (
    SELECT
      app_role.is_admin ()
  )
)
;

--------------------------------------------------------------------------------
-- 5. Provider Table
--------------------------------------------------------------------------------
-- 5.1. Provider Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
  app_role.provider (
    user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
  )
;

--------------------------------------------------------------------------------
-- 5.2. Provider RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_role.provider ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "provider_select_all" ON app_role.provider FOR
SELECT
  USING (TRUE)
;

CREATE POLICY "provider_insert_admin" ON app_role.provider FOR INSERT
WITH
  CHECK (
    (
      SELECT
        app_role.is_admin ()
    )
  )
;

CREATE POLICY "provider_update_admin" ON app_role.provider FOR
UPDATE USING (
  (
    SELECT
      app_role.is_admin ()
  )
)
WITH
  CHECK (
    (
      SELECT
        app_role.is_admin ()
    )
  )
;

CREATE POLICY "provider_delete_admin" ON app_role.provider FOR DELETE USING (
  (
    SELECT
      app_role.is_admin ()
  )
)
;