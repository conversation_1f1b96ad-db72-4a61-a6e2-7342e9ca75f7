DROP POLICY IF EXISTS "avatar_select_all" ON STORAGE.objects
;

DROP POLICY IF EXISTS "avatar_insert_own" ON STORAGE.objects
;

DROP POLICY IF EXISTS "avatar_update_own" ON STORAGE.objects
;

DROP POLICY IF EXISTS "avatar_delete_own" ON STORAGE.objects
;

CREATE POLICY "avatar_select_all" ON STORAGE.objects FOR
SELECT
    USING (bucket_id = 'avatar')
;

CREATE POLICY "avatar_insert_own" ON STORAGE.objects FOR INSERT TO authenticated
WITH
    CHECK (
        bucket_id = 'avatar'
        AND (STORAGE.filename (NAME)) = (
            (
                SELECT
                    auth.uid ()
            )::TEXT
        )
    )
;

CREATE POLICY "avatar_update_own" ON STORAGE.objects FOR
UPDATE TO authenticated USING (
    bucket_id = 'avatar'
    AND (STORAGE.filename (NAME)) = (
        (
            SELECT
                auth.uid ()
        )::TEXT
    )
)
;

CREATE POLICY "avatar_delete_own" ON STORAGE.objects FOR DELETE TO authenticated USING (
    bucket_id = 'avatar'
    AND (STORAGE.filename (NAME)) = (
        (
            SELECT
                auth.uid ()
        )::TEXT
    )
)
;