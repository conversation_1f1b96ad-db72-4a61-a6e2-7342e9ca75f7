--------------------------------------------------------------------------------
-- Table of Contents
--------------------------------------------------------------------------------
-- 1. Schema
--    1.1. Schema Cleanup
--    1.2. Schema Setup
-- 2. Views
--    2.1. Pending Withdrawals View
--------------------------------------------------------------------------------
-- 1. Schema
--------------------------------------------------------------------------------
-- 1.1. Schema Cleanup
--------------------------------------------------------------------------------
DROP SCHEMA IF EXISTS app_dashboard CASCADE
;

--------------------------------------------------------------------------------
-- 1.2. Schema Setup
--------------------------------------------------------------------------------
CREATE SCHEMA IF NOT EXISTS app_dashboard
;

GRANT USAGE ON SCHEMA app_dashboard TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_dashboard TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_dashboard TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_dashboard TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_dashboard
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_dashboard
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_dashboard
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

--------------------------------------------------------------------------------
-- 2. Views
--------------------------------------------------------------------------------
-- 2.1. Pending Withdrawals View
--------------------------------------------------------------------------------
-- CREATE OR REPLACE VIEW
--   app_dashboard.withdrawal AS
-- SELECT
--   id,
--   user_id,
--   paid_amount_in_withdrawal_currency AS payment,
--   commission_amount_in_withdrawal_currency AS commission,
--   status,
--   currency
-- FROM
--   app_transaction.withdrawal
-- WHERE
--   status = 'pending'
-- ;