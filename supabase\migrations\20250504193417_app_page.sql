--------------------------------------------------------------------------------
-- Table of Contents:
--------------------------------------------------------------------------------
-- Schema
-- Cleaning
-- Client View
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- Schema
--------------------------------------------------------------------------------
CREATE SCHEMA IF NOT EXISTS app_page
;

GRANT USAGE ON SCHEMA app_page TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_page TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_page TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_page TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_page
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_page
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_page
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

--------------------------------------------------------------------------------
-- Client View
--------------------------------------------------------------------------------
CREATE VIEW
    app_page.client AS
SELECT
    p.user_id,
    p.name,
    p.honorific,
    p.gender,
    p.join_date,
    p.birth_date,
    p.bio,
    public.get_first_jsonb_value (p.bio) AS default_bio,
    i.name AS avatar
FROM
    app_account.profile AS p
    LEFT JOIN app_media.image AS i ON p.user_id = i.name::UUID
    AND i.bucket_id = 'avatar'
;