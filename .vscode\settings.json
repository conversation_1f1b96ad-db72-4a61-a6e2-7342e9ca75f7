{"deno.enablePaths": ["supabase/functions"], "deno.importMap": "./supabase/functions/import_map.json", "deno.lint": true, "deno.enable": true, "deno.unstable": ["jsr:@supabase/supabase-js@2", "bare-node-builtins", "byonm", "sloppy-imports", "unsafe-proto", "webgpu", "broadcast-channel", "worker-options", "cron", "kv", "ffi", "fs", "http", "net"], "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules\\typescript\\lib", "cSpell.words": ["esenpai", "Sen<PERSON><PERSON>"], "i18n-ally.localesPaths": ["packages/shared/locale"], "i18n-ally.keystyle": "nested"}