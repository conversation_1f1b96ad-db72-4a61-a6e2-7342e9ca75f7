--------------------------------------------------------------------------------
-- Table of Contents
--------------------------------------------------------------------------------
-- 1. Schema
--    1.1. Schema Cleanup
--    1.2. Schema Setup
-- 2. Image Table
--    2.1. Image Table Definition
--    2.2. Image Table RLS Policies
--    2.3. Image Table Indexes
--    2.4. Image Table Triggers
--        2.4.1. register_storage_object_as_image Function
--        2.4.2. storage_objects_insert_image Trigger
-- 3. Data Migration
--------------------------------------------------------------------------------
-- 1. Schema
--------------------------------------------------------------------------------
-- 1.1. Schema Cleanup
--------------------------------------------------------------------------------
DROP SCHEMA IF EXISTS app_media CASCADE
;

--------------------------------------------------------------------------------
-- 1.2. Schema Setup
--------------------------------------------------------------------------------
CREATE SCHEMA IF NOT EXISTS app_media
;

GRANT USAGE ON SCHEMA app_media TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_media TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_media TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_media TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_media
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_media
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_media
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

--------------------------------------------------------------------------------
-- 2. Image Table
--------------------------------------------------------------------------------
-- 2.1. Image Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
  app_media.image (
    object_id UUID PRIMARY KEY REFERENCES STORAGE.objects (id) ON DELETE CASCADE,
    bucket_id TEXT NOT NULL,
    NAME TEXT NOT NULL,
    base64_placeholder TEXT
  )
;

--------------------------------------------------------------------------------
-- 2.2. Image Table RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_media.image ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "image_select_all" ON app_media.image FOR
SELECT
  USING (TRUE)
;

--------------------------------------------------------------------------------
-- 2.3. Image Table Indexes
--------------------------------------------------------------------------------
CREATE INDEX image_bucket_id_idx ON app_media.image (bucket_id)
;

CREATE INDEX image_name_idx ON app_media.image (NAME)
;

--------------------------------------------------------------------------------
-- 2.4. Image Table Triggers
--------------------------------------------------------------------------------
-- Function to handle image insertion from storage objects
CREATE
OR REPLACE FUNCTION app_media.register_storage_object_as_image () RETURNS TRIGGER LANGUAGE plpgsql SECURITY DEFINER -- Use SECURITY DEFINER to allow insertion into app_media.image
AS $$
BEGIN
    -- Check if metadata is not null and contains mimetype
    IF NEW.metadata IS NOT NULL AND NEW.metadata ? 'mimetype' THEN
        -- Check if the mimetype indicates an image
        IF NEW.metadata->>'mimetype' LIKE 'image/%' THEN
            -- Insert into app_media.image table
            INSERT INTO app_media.image (object_id, bucket_id, name)
            VALUES (NEW.id, NEW.bucket_id, NEW.name);
        END IF;
    END IF;

    RETURN NEW;
END;
$$
;

-- Trigger to call the function after inserting into storage.objects
CREATE TRIGGER storage_objects_insert_image
AFTER INSERT ON STORAGE.objects FOR EACH ROW
EXECUTE FUNCTION app_media.register_storage_object_as_image ()
;

--------------------------------------------------------------------------------
-- 2.5. Image Table After Insert Trigger
--------------------------------------------------------------------------------
CREATE
OR REPLACE FUNCTION app_media.image_after_insert () RETURNS TRIGGER LANGUAGE plpgsql AS $$
DECLARE
  api_url TEXT;
  api_call_secret TEXT;
BEGIN
  -- Get the API_URL secret from the vault
  SELECT decrypted_secret INTO api_url
  FROM vault.decrypted_secrets
  WHERE name = 'API_URL';

  -- Get the API_CALL_SECRET secret from the vault
  SELECT decrypted_secret INTO api_call_secret
  FROM vault.decrypted_secrets
  WHERE name = 'API_CALL_SECRET';

  -- Call the edge function to generate the base64 placeholder
  PERFORM net.http_post(
    url := api_url || '/base64',
    body := jsonb_build_object(
      'name', NEW.name,
      'object_id', NEW.object_id,
      'bucket_id', NEW.bucket_id
    ),
    headers := jsonb_build_object(
      'Content-Type', 'application/json',
      'X-API-CALL-SECRET', api_call_secret
    ),
    timeout_milliseconds := 30000
  );

  RETURN NULL;
END;
$$
;

CREATE TRIGGER image_after_insert_trigger
AFTER INSERT ON app_media.image FOR EACH ROW
EXECUTE FUNCTION app_media.image_after_insert ()
;

--------------------------------------------------------------------------------
-- 3. Data Migration
--------------------------------------------------------------------------------
-- Register existing storage objects as app_media.image
INSERT INTO
  app_media.image (object_id, bucket_id, NAME)
SELECT
  id,
  bucket_id,
  NAME
FROM
  STORAGE.objects
WHERE
  metadata IS NOT NULL
  AND metadata ? 'mimetype'
  AND metadata ->> 'mimetype' LIKE 'image/%' ON CONFLICT (object_id)
DO NOTHING
;