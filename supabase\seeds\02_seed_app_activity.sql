--------------------------------------------------------------------------------
-- TABLE OF CONTENTS
--------------------------------------------------------------------------------
-- 1. Cleanup Operations
-- 2. Activity Group: Gaming
-- 3. Activity Activities: Most Played Video Games
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 1. Cleanup Operations
--------------------------------------------------------------------------------
-- No cleanup operations needed as this is a seed file
--------------------------------------------------------------------------------
-- 2. Activity Group: Gaming
--------------------------------------------------------------------------------
INSERT INTO
    app_activity.category (NAME, description, slug)
VALUES
    (
        '{
    "en": "Gaming",
    "ko": "비디오 게임",
    "ja": "ビデオゲーム",
    "tr": "Oyun"
  }',
        '{"en": "Let''s game with Senpai!","ko": "선배와 함께 게임해요!","ja": "先輩と一緒にゲームしよう！","tr": "Senpai ile oyun oynayalım!"}',
        'gaming'
    ) ON CONFLICT (NAME)
DO NOTHING
;

--------------------------------------------------------------------------------
-- 2. Activity Group: Reacting
--------------------------------------------------------------------------------
INSERT INTO
    app_activity.category (NAME, description, slug)
VALUES
    (
        '{"en": "Reacting","ko": "반응","ja": "反応","tr": "Reaksiyon"}',
        '{"en": "Let''s react with Senpai!","ko": "선배와 함께 반응해봐요!","ja": "先輩と一緒にリアクションしよう！","tr": "Senpai ile tepki verelim!"}',
        'reacting'
    ) ON CONFLICT (NAME)
DO NOTHING
;

--------------------------------------------------------------------------------
-- 2. Activity Group: Creating
--------------------------------------------------------------------------------
INSERT INTO
    app_activity.category (NAME, description, slug)
VALUES
    (
        '{"en": "Creating","ko": "창작","ja": "作成","tr": "Yaratıcılık"}',
        '{"en": "Let''s create with Senpai!","ko": "선배와 함께 만들어봐요!","ja": "先輩と一緒に創造しよう！","tr": "Senpai ile bir şeyler yaratalım!"}',
        'creating'
    ) ON CONFLICT (NAME)
DO NOTHING
;

--------------------------------------------------------------------------------
-- 2. Activity Group: Relaxing
--------------------------------------------------------------------------------
INSERT INTO
    app_activity.category (NAME, description, slug)
VALUES
    (
        '{"en": "Relaxing","ko": "휴식","ja": "リラックス","tr": "Rahatlama"}',
        '{"en": "Let''s relax with Senpai!","ko": "선배와 함께 휴식해요!","ja": "先輩と一緒にリラックスしよう！","tr": "Senpai ile rahatlayalım!"}',
        'relaxing'
    ) ON CONFLICT (NAME)
DO NOTHING
;

--------------------------------------------------------------------------------
-- 3. Activity Activities: Most Played Video Games
--------------------------------------------------------------------------------
-- Get the id of the "Video Games" group
WITH
    video_games_group AS (
        SELECT
            id
        FROM
            app_activity.category
        WHERE
            NAME = '{ "en": "Gaming", "ko": "게이밍", "ja": "ゲーミング", "tr": "Oyun" }'
    )
INSERT INTO
    app_activity.activity (category_id, NAME, description, color, slug)
SELECT
    (
        SELECT
            id
        FROM
            video_games_group
    ),
    '{"en": "League of Legends", "ko": "리그 오브 레전드", "ja": "リーグ・オブ・レジェンド", "tr": "League of Legends"}',
    '{"en": "Command your champions and conquer the Rift in this strategic battle for supremacy.", "ko": "챔피언을 지휘하고 패권을 위한 이 전략적 전투에서 리프트를 정복하세요.", "ja": "チャンピオンを指揮して、覇権を争うこの戦略的な戦いでリフトを征服しよう。", "tr": "Şampiyonlarınıza komuta edin ve bu stratejik hakimiyet savaşında Rift''i fethedin."}',
    '"#53a0e5"',
    'league-of-legends' ON CONFLICT (category_id, NAME)
DO NOTHING
;

WITH
    video_games_group AS (
        SELECT
            id
        FROM
            app_activity.category
        WHERE
            NAME = '{ "en": "Gaming", "ko": "게이밍", "ja": "ゲーミング", "tr": "Oyun" }'
    )
INSERT INTO
    app_activity.activity (category_id, NAME, description, color, slug)
SELECT
    (
        SELECT
            id
        FROM
            video_games_group
    ),
    '{"en": "Fortnite", "ko": "포트나이트", "ja": "フォートナイト", "tr": "Fortnite"}',
    '{"en": "Build, battle, and survive in the ever-evolving world of Fortnite, where only the strongest squad prevails.", "ko": "가장 강력한 분대만이 승리하는 끊임lessly evolving Fortnite world, where only the strongest squad prevails.", "ja": "最強の部隊だけが勝利する、進化し続けるFortniteの世界で、建設し、戦い、生き残ろう。", "tr": "Sadece en güçlü takımın galip geldiği, sürekli gelişen Fortnite dünyasında inşa edin, savaşın ve hayatta kalın."}',
    '{"light": "#733a8c", "dark": "#a0522d"}',
    'fortnite' ON CONFLICT (category_id, NAME)
DO NOTHING
;

WITH
    video_games_group AS (
        SELECT
            id
        FROM
            app_activity.category
        WHERE
            NAME = '{ "en": "Gaming", "ko": "게이밍", "ja": "ゲーミング", "tr": "Oyun" }'
    )
INSERT INTO
    app_activity.activity (category_id, NAME, description, color, slug)
SELECT
    (
        SELECT
            id
        FROM
            video_games_group
    ),
    '{"en": "Minecraft", "ko": "마인크래프트", "ja": "マインクラフト", "tr": "Minecraft"}',
    '{"en": "Unleash your creativity in Minecraft''s limitless sandbox, where you can build anything you imagine.", "ko": "상상하는 모든 것을 건설할 수 있는 Minecraft의 무한한 샌드박스에서 창의력을 발휘하세요.", "ja": "想像できるものは何でも構築できるMinecraftの無限のサンドボックス에서創造性を発揮しよう。", "tr": "Hayal ettiğiniz her şeyi inşa edebileceğiniz Minecraft''ın sınırsız sanal alanında yaratıcılığınızı ortaya çıkarın."}',
    '{"light": "#3ab54a", "dark": "#228b22"}',
    'minecraft' ON CONFLICT (category_id, NAME)
DO NOTHING
;

WITH
    video_games_group AS (
        SELECT
            id
        FROM
            app_activity.category
        WHERE
            NAME = '{ "en": "Gaming", "ko": "게이밍", "ja": "ゲーミング", "tr": "Oyun" }'
    )
INSERT INTO
    app_activity.activity (category_id, NAME, description, color, slug)
SELECT
    (
        SELECT
            id
        FROM
            video_games_group
    ),
    '{"en": "Counter-Strike: Global Offensive", "ko": "카운터 스트라이크: 글로벌 오펜시브", "ja": "カウンターストライク：グローバルオフェンシブ", "tr": "Counter-Strike: Global Offensive"}',
    '{"en": "Engage in intense tactical battles in Counter-Strike: Global Offensive, the ultimate test of skill and teamwork.", "ko": "궁극의 기술과 팀워크 시험인 Counter-Strike: Global Offensive에서 치열한 전술 전투에 참여하세요.", "ja": "究極のスキルとチームワークのテストであるCounter-Strike: Global Offensiveで、激しい戦術的戦闘に参加しよう。", "tr": "Nihai beceri ve takım çalışması testi olan Counter-Strike: Global Offensive''de yoğun taktiksel savaşlara katılın."}',
    '"#f39e32"',
    'counter-strike-global-offensive' ON CONFLICT (category_id, NAME)
DO NOTHING
;

WITH
    video_games_group AS (
        SELECT
            id
        FROM
            app_activity.category
        WHERE
            NAME = '{ "en": "Gaming", "ko": "게이밍", "ja": "ゲーミング", "tr": "Oyun" }'
    )
INSERT INTO
    app_activity.activity (category_id, NAME, description, color, slug)
SELECT
    (
        SELECT
            id
        FROM
            video_games_group
    ),
    '{"en": "Grand Theft Auto V", "ko": "Grand Theft Auto V", "ja": "グランド・セフト・オートV", "tr": "Grand Theft Auto V"}',
    '{"en": "Explore the sprawling metropolis of Los Santos in Grand Theft Auto V, where crime and chaos reign supreme.", "ko": "범죄와 혼돈이 지배하는 Grand Theft Auto V에서 로스 산토스의 광활한 대도시를 탐험하세요.", "ja": "犯罪と混沌が支配するGrand Theft Auto Vで、ロスサントスの広大な大都市を探索しよう。", "tr": "Suç ve kaosun hüküm sürdüğü Grand Theft Auto V''te Los Santos''un genişleyen metropolünü keşfedin."}',
    '"#4a6b4a"',
    'grand-theft-auto-v' ON CONFLICT (category_id, NAME)
DO NOTHING
;

--------------------------------------------------------------------------------
-- 3. Activity Activities: Reacting
--------------------------------------------------------------------------------
-- Get the id of the "Reacting" group
WITH
    reacting_group AS (
        SELECT
            id
        FROM
            app_activity.category
        WHERE
            NAME = '{"en": "Reacting","ko": "반응","ja": "反応","tr": "Reaksiyon"}'
    )
INSERT INTO
    app_activity.activity (category_id, NAME, description, color, slug)
SELECT
    (
        SELECT
            id
        FROM
            reacting_group
    ),
    '{"en": "Movie", "ko": "영화", "ja": "映画", "tr": "Film"}',
    '{"en": "React to a movie with Senpai!", "ko": "선배와 함께 영화에 반응해봐요!", "ja": "先輩と一緒に映画にリアクションしよう！", "tr": "Senpai ile bir filme tepki verelim!"}',
    '"#ff4500"',
    'movie' ON CONFLICT (category_id, NAME)
DO NOTHING
;

WITH
    reacting_group AS (
        SELECT
            id
        FROM
            app_activity.category
        WHERE
            NAME = '{"en": "Reacting","ko": "반응","ja": "反応","tr": "Reaksiyon"}'
    )
INSERT INTO
    app_activity.activity (category_id, NAME, description, color, slug)
SELECT
    (
        SELECT
            id
        FROM
            reacting_group
    ),
    '{"en": "Anime", "ko": "애니메이션", "ja": "アニメ", "tr": "Anime"}',
    '{"en": "React to an anime with Senpai!", "ko": "선배와 함께 애니메이션에 반응해봐요!", "ja": "先輩と一緒にアニメにリアクションしよう！", "tr": "Senpai ile bir animeye tepki verelim!"}',
    '"#00bfff"',
    'anime' ON CONFLICT (category_id, NAME)
DO NOTHING
;

WITH
    reacting_group AS (
        SELECT
            id
        FROM
            app_activity.category
        WHERE
            NAME = '{"en": "Reacting","ko": "반응","ja": "反応","tr": "Reaksiyon"}'
    )
INSERT INTO
    app_activity.activity (category_id, NAME, description, color, slug)
SELECT
    (
        SELECT
            id
        FROM
            reacting_group
    ),
    '{"en": "Cartoon", "ko": "만화", "ja": "漫画", "tr": "Çizgi Film"}',
    '{"en": "React to a cartoon with Senpai!", "ko": "선배와 함께 만화에 반응해봐요!", "ja": "先輩と一緒に漫画にリアクションしよう！", "tr": "Senpai ile bir çizgi filme tepki verelim!"}',
    '"#ffff00"',
    'cartoon' ON CONFLICT (category_id, NAME)
DO NOTHING
;

WITH
    reacting_group AS (
        SELECT
            id
        FROM
            app_activity.category
        WHERE
            NAME = '{"en": "Reacting","ko": "반응","ja": "反応","tr": "Reaksiyon"}'
    )
INSERT INTO
    app_activity.activity (category_id, NAME, description, color, slug)
SELECT
    (
        SELECT
            id
        FROM
            reacting_group
    ),
    '{"en": "YouTube", "ko": "유튜브", "ja": "ユーチューブ", "tr": "YouTube"}',
    '{"en": "React to a YouTube video with Senpai!", "ko": "선배와 함께 유튜브 영상에 반응해봐요!", "ja": "先輩と一緒にユーチューブの動画にリアクションしよう！", "tr": "Senpai ile bir YouTube videosuna tepki verelim!"}',
    '"#ff0000"',
    'youtube' ON CONFLICT (category_id, NAME)
DO NOTHING
;

WITH
    reacting_group AS (
        SELECT
            id
        FROM
            app_activity.category
        WHERE
            NAME = '{"en": "Reacting","ko": "반응","ja": "反応","tr": "Reaksiyon"}'
    )
INSERT INTO
    app_activity.activity (category_id, NAME, description, color, slug)
SELECT
    (
        SELECT
            id
        FROM
            reacting_group
    ),
    '{"en": "TV Show", "ko": "TV 프로그램", "ja": "テレビ番組", "tr": "TV Şovu"}',
    '{"en": "React to a TV show with Senpai!", "ko": "선배와 함께 TV 프로그램에 반응해봐요!", "ja": "先輩と一緒にテレビ番組にリアクションしよう！", "tr": "Senpai ile bir TV şovuna tepki verelim!"}',
    '"#9400d3"',
    'tv-show' ON CONFLICT (category_id, NAME)
DO NOTHING
;