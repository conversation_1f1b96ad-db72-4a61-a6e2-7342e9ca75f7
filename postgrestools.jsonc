{"$schema": "https://pgtools.dev/schemas/0.0.0/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignore": []}, "linter": {"enabled": true, "rules": {"recommended": true}}, "db": {"host": "127.0.0.1", "port": 54322, "username": "postgres", "password": "postgres", "database": "postgres", "connTimeoutSecs": 10, "allowStatementExecutionsAgainst": ["127.0.0.1/*", "localhost/*"]}}