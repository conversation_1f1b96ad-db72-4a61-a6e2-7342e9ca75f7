# E-Senpai Testing Guide

## Introduction

This guide provides an overview of the testing practices and structure within the E-Senpai project. We utilize Vitest as our primary testing framework to ensure the reliability and correctness of our codebase, covering various aspects from email services to Supabase interactions.

## Test Framework

E-Senpai uses [Vitest](https://vitest.dev/) as its testing framework. Vitest is a fast and modern test runner designed for JavaScript and TypeScript projects. It offers a familiar API (compatible with Jest) and excellent performance.

## Test File Structure

Tests are organized into directories based on the module or service they cover.

-   **`tests/email/`**: Contains tests related to the email service integration, specifically for Mailgun and React Email.
    -   `otp.test.ts`: Tests the OTP email sending functionality.
    -   `README.md`: Provides setup and running instructions for email tests.

-   **`tests/supabase/`**: Contains tests for Supabase interactions, including database RLS policies and storage operations.
    -   `app_account.availability.test.ts`: Focuses on testing availability overlapping logic within the `app_account` schema.
    -   `setup.ts`: Contains setup and teardown logic for Supabase tests, including creating and deleting test users.
    -   `storage.rls.test.ts`: Verifies Row Level Security (RLS) policies for Supabase storage, ensuring proper user-id based access.
    -   `storage.upsert.test.ts`: Tests the upsert functionality for Supabase storage, ensuring object IDs remain consistent after updates.
    -   `objects/`: Contains test assets (e.g., images) used in storage tests.

## Running Tests

### Running All Tests

To run all tests in the project, use the following command:

```bash
pnpm test
```

### Running Specific Test Suites

You can run tests for a specific suite by using the `pnpm test` command followed by the path to the test directory or file.

-   **Email Tests**:
    ```bash
    pnpm test:email
    ```
    (As defined in `tests/email/README.md`, this script specifically targets email tests.)

-   **Supabase Tests**:
    ```bash
    pnpm test tests/supabase/
    ```
    This will run all tests within the `tests/supabase` directory.

-   **Individual Test File**:
    ```bash
    pnpm test tests/email/otp.test.ts
    ```
    You can also run a specific test file by providing its full path.

**Important**: Always use `pnpm` scripts for running tests to avoid potential issues with JSX syntax or environment setup.
**Note**: Do not run test commands in watch mode, as this can lead to unexpected behavior or resource consumption.