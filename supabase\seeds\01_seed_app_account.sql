--------------------------------------------------------------------------------
-- Table of Contents:
--------------------------------------------------------------------------------
-- Auth Users
-- Profile
-- Profile
-- KYC
-- Admin
-- Celebrity
-- IBAN
-- Locale
-- Bio Data
-- Privacy
--------------------------------------------------------------------------------
-- Auth Users
--------------------------------------------------------------------------------
-- Insert admin user
INSERT INTO
    auth.users (id, email, raw_app_meta_data, raw_user_meta_data, is_sso_user)
VALUES
    (
        'c7702895-b2d6-472b-b026-51e19ebbc97c',
        '<EMAIL>',
        '{"provider":"email","providers":["email"]}',
        '{"full_name":"Admin User"}',
        FALSE
    ) ON CONFLICT (id)
DO
UPDATE
SET
    email = EXCLUDED.email,
    raw_app_meta_data = EXCLUDED.raw_app_meta_data,
    raw_user_meta_data = EXCLUDED.raw_user_meta_data,
    is_sso_user = EXCLUDED.is_sso_user
;

-- Insert regular user (female gamer)
INSERT INTO
    auth.users (id, email, raw_app_meta_data, raw_user_meta_data, is_sso_user)
VALUES
    (
        'a1b2c3d4-e5f6-7788-9900-aabbccddeeff',
        '<EMAIL>',
        '{"provider":"email","providers":["email"]}',
        '{"full_name":"Ayumi Tanaka"}',
        FALSE
    ) ON CONFLICT (id)
DO
UPDATE
SET
    email = EXCLUDED.email,
    raw_app_meta_data = EXCLUDED.raw_app_meta_data,
    raw_user_meta_data = EXCLUDED.raw_user_meta_data,
    is_sso_user = EXCLUDED.is_sso_user
;

-- Insert gaming coach user
INSERT INTO
    auth.users (id, email, raw_app_meta_data, raw_user_meta_data, is_sso_user)
VALUES
    (
        '********-4455-6677-8899-aabbccddeeff',
        '<EMAIL>',
        '{"provider":"email","providers":["email"]}',
        '{"full_name":"Min-Jun Park"}',
        FALSE
    ) ON CONFLICT (id)
DO
UPDATE
SET
    email = EXCLUDED.email,
    raw_app_meta_data = EXCLUDED.raw_app_meta_data,
    raw_user_meta_data = EXCLUDED.raw_user_meta_data,
    is_sso_user = EXCLUDED.is_sso_user
;

-- Insert celebrity streamer user
INSERT INTO
    auth.users (id, email, raw_app_meta_data, raw_user_meta_data, is_sso_user)
VALUES
    (
        'e9876543-210f-edcb-a987-6543210fedcb',
        '<EMAIL>',
        '{"provider":"email","providers":["email"]}',
        '{"full_name":"Yildiz Aydin"}',
        FALSE
    ) ON CONFLICT (id)
DO
UPDATE
SET
    email = EXCLUDED.email,
    raw_app_meta_data = EXCLUDED.raw_app_meta_data,
    raw_user_meta_data = EXCLUDED.raw_user_meta_data,
    is_sso_user = EXCLUDED.is_sso_user
;

-- Insert Discord SSO user
INSERT INTO
    auth.users (id, email, raw_app_meta_data, raw_user_meta_data, is_sso_user)
VALUES
    (
        'b5f8c1e7-d8a3-4b1c-9e5d-2f7a6b4c8d9e',
        '<EMAIL>',
        '{"provider":"discord","providers":["discord"]}',
        '{"full_name":"Kenji Sato", "avatar_url":"https://example.com/discord_avatars/kenji.jpg"}',
        TRUE
    ) ON CONFLICT (id)
DO
UPDATE
SET
    email = EXCLUDED.email,
    raw_app_meta_data = EXCLUDED.raw_app_meta_data,
    raw_user_meta_data = EXCLUDED.raw_user_meta_data,
    is_sso_user = EXCLUDED.is_sso_user
;

-- Insert Google SSO user
INSERT INTO
    auth.users (id, email, raw_app_meta_data, raw_user_meta_data, is_sso_user)
VALUES
    (
        'd4e5f6a7-b8c9-4d5e-6f7a-8b9c0d1e2f3a',
        '<EMAIL>',
        '{"provider":"google","providers":["google"]}',
        '{"full_name":"Emma Wilson", "avatar_url":"https://example.com/google_avatars/emma.jpg"}',
        TRUE
    ) ON CONFLICT (id)
DO
UPDATE
SET
    email = EXCLUDED.email,
    raw_app_meta_data = EXCLUDED.raw_app_meta_data,
    raw_user_meta_data = EXCLUDED.raw_user_meta_data,
    is_sso_user = EXCLUDED.is_sso_user
;

--------------------------------------------------------------------------------
-- Profile
--------------------------------------------------------------------------------
-- Seed data for account.profile table
INSERT INTO
    app_account.profile (user_id, NAME, gender, birth_date, honorific)
VALUES
    (
        'c7702895-b2d6-472b-b026-51e19ebbc97c',
        'AdminSan',
        'male',
        '1988-06-15',
        'sama'
    ) ON CONFLICT (user_id)
DO
UPDATE
SET
    NAME = EXCLUDED.name,
    gender = EXCLUDED.gender,
    birth_date = EXCLUDED.birth_date,
    honorific = EXCLUDED.honorific
;

INSERT INTO
    app_account.profile (user_id, NAME, gender, birth_date, honorific)
VALUES
    (
        'a1b2c3d4-e5f6-7788-9900-aabbccddeeff',
        'AyumiChan',
        'female',
        '1997-05-10',
        'chan'
    ) ON CONFLICT (user_id)
DO
UPDATE
SET
    NAME = EXCLUDED.name,
    gender = EXCLUDED.gender,
    birth_date = EXCLUDED.birth_date,
    honorific = EXCLUDED.honorific
;

INSERT INTO
    app_account.profile (user_id, NAME, gender, birth_date, honorific)
VALUES
    (
        'e9876543-210f-edcb-a987-6543210fedcb',
        'YildizTV',
        'female',
        '1994-03-18',
        'san'
    ) ON CONFLICT (user_id)
DO
UPDATE
SET
    NAME = EXCLUDED.name,
    gender = EXCLUDED.gender,
    birth_date = EXCLUDED.birth_date,
    honorific = EXCLUDED.honorific
;

INSERT INTO
    app_account.profile (user_id, NAME, gender, birth_date, honorific)
VALUES
    (
        '********-4455-6677-8899-aabbccddeeff',
        'CoachPark',
        'male',
        '1992-11-20',
        'sama'
    ) ON CONFLICT (user_id)
DO
UPDATE
SET
    NAME = EXCLUDED.name,
    gender = EXCLUDED.gender,
    birth_date = EXCLUDED.birth_date,
    honorific = EXCLUDED.honorific
;

INSERT INTO
    app_account.profile (user_id, NAME, gender, birth_date, honorific)
VALUES
    ('b5f8c1e7-d8a3-4b1c-9e5d-2f7a6b4c8d9e', 'KenjiGG', 'male', '1996-08-12', 'kun') ON CONFLICT (user_id)
DO
UPDATE
SET
    NAME = EXCLUDED.name,
    gender = EXCLUDED.gender,
    birth_date = EXCLUDED.birth_date,
    honorific = EXCLUDED.honorific
;

INSERT INTO
    app_account.profile (user_id, NAME, gender, birth_date, honorific)
VALUES
    (
        'd4e5f6a7-b8c9-4d5e-6f7a-8b9c0d1e2f3a',
        'EmmaPlays',
        'female',
        '1999-12-05',
        'chan'
    ) ON CONFLICT (user_id)
DO
UPDATE
SET
    NAME = EXCLUDED.name,
    gender = EXCLUDED.gender,
    birth_date = EXCLUDED.birth_date,
    honorific = EXCLUDED.honorific
;

--------------------------------------------------------------------------------
-- KYC
--------------------------------------------------------------------------------
-- Seed data for app_account.kyc table
INSERT INTO
    app_account.kyc (user_id, status, full_name)
VALUES
    ('c7702895-b2d6-472b-b026-51e19ebbc97c', 'approved', 'Admin User') ON CONFLICT (user_id)
DO
UPDATE
SET
    status = EXCLUDED.status,
    full_name = EXCLUDED.full_name
;

INSERT INTO
    app_account.kyc (user_id, status, full_name)
VALUES
    ('a1b2c3d4-e5f6-7788-9900-aabbccddeeff', 'pending', 'Ayumi Tanaka') ON CONFLICT (user_id)
DO
UPDATE
SET
    status = EXCLUDED.status,
    full_name = EXCLUDED.full_name
;

INSERT INTO
    app_account.kyc (user_id, status, full_name)
VALUES
    ('e9876543-210f-edcb-a987-6543210fedcb', 'approved', 'Yildiz Aydin') ON CONFLICT (user_id)
DO
UPDATE
SET
    status = EXCLUDED.status,
    full_name = EXCLUDED.full_name
;

INSERT INTO
    app_account.kyc (user_id, status, full_name)
VALUES
    ('********-4455-6677-8899-aabbccddeeff', 'approved', 'Min-Jun Park') ON CONFLICT (user_id)
DO
UPDATE
SET
    status = EXCLUDED.status,
    full_name = EXCLUDED.full_name
;

INSERT INTO
    app_account.kyc (user_id, status, full_name)
VALUES
    ('b5f8c1e7-d8a3-4b1c-9e5d-2f7a6b4c8d9e', 'draft', 'Kenji Sato') ON CONFLICT (user_id)
DO
UPDATE
SET
    status = EXCLUDED.status,
    full_name = EXCLUDED.full_name
;

INSERT INTO
    app_account.kyc (user_id, status, full_name)
VALUES
    ('d4e5f6a7-b8c9-4d5e-6f7a-8b9c0d1e2f3a', 'rejected', 'Emma Wilson') ON CONFLICT (user_id)
DO
UPDATE
SET
    status = EXCLUDED.status,
    full_name = EXCLUDED.full_name
;

--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- Profile
--------------------------------------------------------------------------------
-- Seed data for app_provider.profile table
INSERT INTO
    app_provider.profile (user_id, slug)
VALUES
    ('a1b2c3d4-e5f6-7788-9900-aabbccddeeff', 'ayumi-chan') ON CONFLICT (user_id)
DO NOTHING
;

INSERT INTO
    app_provider.profile (user_id, slug)
VALUES
    ('e9876543-210f-edcb-a987-6543210fedcb', 'yildiz-tv') ON CONFLICT (user_id)
DO NOTHING
;

INSERT INTO
    app_provider.profile (user_id, slug)
VALUES
    ('********-4455-6677-8899-aabbccddeeff', 'coach-park') ON CONFLICT (user_id)
DO NOTHING
;

INSERT INTO
    app_provider.profile (user_id, slug)
VALUES
    ('b5f8c1e7-d8a3-4b1c-9e5d-2f7a6b4c8d9e', 'kenji-gg') ON CONFLICT (user_id)
DO NOTHING
;

--------------------------------------------------------------------------------
-- Admin
--------------------------------------------------------------------------------
-- Seed data for app_role.admin table
INSERT INTO
    app_role.admin (user_id)
VALUES
    ('c7702895-b2d6-472b-b026-51e19ebbc97c') ON CONFLICT (user_id)
DO NOTHING
;

--------------------------------------------------------------------------------
-- Celebrity
--------------------------------------------------------------------------------
-- Seed data for app_role.celebrity table
INSERT INTO
    app_role.celebrity (user_id)
VALUES
    ('e9876543-210f-edcb-a987-6543210fedcb') ON CONFLICT (user_id)
DO NOTHING
;

-- Add another celebrity
INSERT INTO
    app_role.celebrity (user_id)
VALUES
    ('d4e5f6a7-b8c9-4d5e-6f7a-8b9c0d1e2f3a') ON CONFLICT (user_id)
DO NOTHING
;

--------------------------------------------------------------------------------
-- IBAN
--------------------------------------------------------------------------------
-- Seed data for app_account.iban table
INSERT INTO
    app_account.iban (user_id, iban)
VALUES
    ('c7702895-b2d6-472b-b026-51e19ebbc97c', '**************************') ON CONFLICT (user_id)
DO NOTHING
;

INSERT INTO
    app_account.iban (user_id, iban)
VALUES
    ('a1b2c3d4-e5f6-7788-9900-aabbccddeeff', '*********************') ON CONFLICT (user_id)
DO NOTHING
;

INSERT INTO
    app_account.iban (user_id, iban)
VALUES
    ('e9876543-210f-edcb-a987-6543210fedcb', '**************************') ON CONFLICT (user_id)
DO NOTHING
;

INSERT INTO
    app_account.iban (user_id, iban)
VALUES
    ('********-4455-6677-8899-aabbccddeeff', '***************************') ON CONFLICT (user_id)
DO NOTHING
;

INSERT INTO
    app_account.iban (user_id, iban)
VALUES
    ('b5f8c1e7-d8a3-4b1c-9e5d-2f7a6b4c8d9e', '*********************') ON CONFLICT (user_id)
DO NOTHING
;

INSERT INTO
    app_account.iban (user_id, iban)
VALUES
    ('d4e5f6a7-b8c9-4d5e-6f7a-8b9c0d1e2f3a', '**********************') ON CONFLICT (user_id)
DO NOTHING
;

--------------------------------------------------------------------------------
-- Locale
--------------------------------------------------------------------------------
-- Seed data for app_account.locale table
INSERT INTO
    app_account.locale (user_id, locale)
VALUES
    ('c7702895-b2d6-472b-b026-51e19ebbc97c', 'en') ON CONFLICT (user_id, locale)
DO NOTHING
;

INSERT INTO
    app_account.locale (user_id, locale)
VALUES
    ('c7702895-b2d6-472b-b026-51e19ebbc97c', 'tr') ON CONFLICT (user_id, locale)
DO NOTHING
;

INSERT INTO
    app_account.locale (user_id, locale)
VALUES
    ('a1b2c3d4-e5f6-7788-9900-aabbccddeeff', 'ja') ON CONFLICT (user_id, locale)
DO NOTHING
;

INSERT INTO
    app_account.locale (user_id, locale)
VALUES
    ('a1b2c3d4-e5f6-7788-9900-aabbccddeeff', 'en') ON CONFLICT (user_id, locale)
DO NOTHING
;

INSERT INTO
    app_account.locale (user_id, locale)
VALUES
    ('e9876543-210f-edcb-a987-6543210fedcb', 'tr') ON CONFLICT (user_id, locale)
DO NOTHING
;

INSERT INTO
    app_account.locale (user_id, locale)
VALUES
    ('e9876543-210f-edcb-a987-6543210fedcb', 'en') ON CONFLICT (user_id, locale)
DO NOTHING
;

INSERT INTO
    app_account.locale (user_id, locale)
VALUES
    ('********-4455-6677-8899-aabbccddeeff', 'ko') ON CONFLICT (user_id, locale)
DO NOTHING
;

INSERT INTO
    app_account.locale (user_id, locale)
VALUES
    ('********-4455-6677-8899-aabbccddeeff', 'en') ON CONFLICT (user_id, locale)
DO NOTHING
;

INSERT INTO
    app_account.locale (user_id, locale)
VALUES
    ('b5f8c1e7-d8a3-4b1c-9e5d-2f7a6b4c8d9e', 'ja') ON CONFLICT (user_id, locale)
DO NOTHING
;

INSERT INTO
    app_account.locale (user_id, locale)
VALUES
    ('b5f8c1e7-d8a3-4b1c-9e5d-2f7a6b4c8d9e', 'en') ON CONFLICT (user_id, locale)
DO NOTHING
;

INSERT INTO
    app_account.locale (user_id, locale)
VALUES
    ('d4e5f6a7-b8c9-4d5e-6f7a-8b9c0d1e2f3a', 'en') ON CONFLICT (user_id, locale)
DO NOTHING
;

--------------------------------------------------------------------------------
-- Bio Data
--------------------------------------------------------------------------------
-- Seed data for app_account.profile bio column
UPDATE app_account.profile
SET
    bio = '{
  "en": "E-Senpai administrator. I help maintain the platform and ensure everyone has a great experience!",
  "tr": "E-Senpai yöneticisi. Platformun bakımını yapıyor ve herkesin harika bir deneyim yaşamasını sağlıyorum!"
}'::jsonb
WHERE
    user_id = 'c7702895-b2d6-472b-b026-51e19ebbc97c'
;

UPDATE app_account.profile
SET
    bio = '{
  "ja": "こんにちは！私はアユミです。ゲームが大好きで、特にValorantとLeague of Legendsをプレイしています。一緒に楽しい時間を過ごしましょう！",
  "en": "Hi! I''m Ayumi. I love gaming, especially Valorant and League of Legends. Let''s have a great time together!"
}'::jsonb
WHERE
    user_id = 'a1b2c3d4-e5f6-7788-9900-aabbccddeeff'
;

UPDATE app_account.profile
SET
    bio = '{
  "tr": "Merhaba! Ben Yıldız, profesyonel Valorant oyuncusu ve yayıncıyım. 3 yıldır rekabetçi sahnede yer alıyorum. Birlikte oynamak ve stratejileri paylaşmak için sabırsızlanıyorum!",
  "en": "Hello! I''m Yildiz, a professional Valorant player and streamer. I''ve been in the competitive scene for 3 years. Looking forward to playing together and sharing strategies!"
}'::jsonb
WHERE
    user_id = 'e9876543-210f-edcb-a987-6543210fedcb'
;

UPDATE app_account.profile
SET
    bio = '{
  "ko": "안녕하세요! 저는 박민준, 프로 게임 코치입니다. 5년 이상의 코칭 경험이 있으며 여러 게임에서 상위 랭크를 달성했습니다. 함께 실력을 향상시켜 봅시다!",
  "en": "Hello! I''m Min-Jun Park, a professional gaming coach. I have over 5 years of coaching experience and have achieved top ranks in multiple games. Let''s improve your skills together!"
}'::jsonb
WHERE
    user_id = '********-4455-6677-8899-aabbccddeeff'
;

UPDATE app_account.profile
SET
    bio = '{
  "ja": "ゲーマーであり、アニメファンです。主にFPSとMOBAをプレイしています。一緒にゲームを楽しみましょう！",
  "en": "Gamer and anime enthusiast. I mainly play FPS and MOBA games. Let''s enjoy gaming together!"
}'::jsonb
WHERE
    user_id = 'b5f8c1e7-d8a3-4b1c-9e5d-2f7a6b4c8d9e'
;

UPDATE app_account.profile
SET
    bio = '{
  "en": "Variety streamer and content creator. I love playing all kinds of games and meeting new people. Join me for some fun gaming sessions!"
}'::jsonb
WHERE
    user_id = 'd4e5f6a7-b8c9-4d5e-6f7a-8b9c0d1e2f3a'
;

--------------------------------------------------------------------------------
-- Privacy
--------------------------------------------------------------------------------
-- Seed data for app_account.privacy table
INSERT INTO
    app_account.privacy (
        user_id,
        show_profile,
        show_activity,
        show_age,
        show_gender,
        show_in_leaderboard
    )
VALUES
    ('c7702895-b2d6-472b-b026-51e19ebbc97c', TRUE, TRUE, TRUE, TRUE, TRUE) ON CONFLICT (user_id)
DO
UPDATE
SET
    show_profile = EXCLUDED.show_profile,
    show_activity = EXCLUDED.show_activity,
    show_age = EXCLUDED.show_age,
    show_gender = EXCLUDED.show_gender,
    show_in_leaderboard = EXCLUDED.show_in_leaderboard
;

INSERT INTO
    app_account.privacy (
        user_id,
        show_profile,
        show_activity,
        show_age,
        show_gender,
        show_in_leaderboard
    )
VALUES
    ('a1b2c3d4-e5f6-7788-9900-aabbccddeeff', TRUE, TRUE, FALSE, TRUE, TRUE) ON CONFLICT (user_id)
DO
UPDATE
SET
    show_profile = EXCLUDED.show_profile,
    show_activity = EXCLUDED.show_activity,
    show_age = EXCLUDED.show_age,
    show_gender = EXCLUDED.show_gender,
    show_in_leaderboard = EXCLUDED.show_in_leaderboard
;

INSERT INTO
    app_account.privacy (
        user_id,
        show_profile,
        show_activity,
        show_age,
        show_gender,
        show_in_leaderboard
    )
VALUES
    ('e9876543-210f-edcb-a987-6543210fedcb', TRUE, TRUE, TRUE, TRUE, TRUE) ON CONFLICT (user_id)
DO
UPDATE
SET
    show_profile = EXCLUDED.show_profile,
    show_activity = EXCLUDED.show_activity,
    show_age = EXCLUDED.show_age,
    show_gender = EXCLUDED.show_gender,
    show_in_leaderboard = EXCLUDED.show_in_leaderboard
;

INSERT INTO
    app_account.privacy (
        user_id,
        show_profile,
        show_activity,
        show_age,
        show_gender,
        show_in_leaderboard
    )
VALUES
    ('********-4455-6677-8899-aabbccddeeff', TRUE, TRUE, TRUE, FALSE, FALSE) ON CONFLICT (user_id)
DO
UPDATE
SET
    show_profile = EXCLUDED.show_profile,
    show_activity = EXCLUDED.show_activity,
    show_age = EXCLUDED.show_age,
    show_gender = EXCLUDED.show_gender,
    show_in_leaderboard = EXCLUDED.show_in_leaderboard
;

INSERT INTO
    app_account.privacy (
        user_id,
        show_profile,
        show_activity,
        show_age,
        show_gender,
        show_in_leaderboard
    )
VALUES
    ('b5f8c1e7-d8a3-4b1c-9e5d-2f7a6b4c8d9e', TRUE, TRUE, FALSE, TRUE, TRUE) ON CONFLICT (user_id)
DO
UPDATE
SET
    show_profile = EXCLUDED.show_profile,
    show_activity = EXCLUDED.show_activity,
    show_age = EXCLUDED.show_age,
    show_gender = EXCLUDED.show_gender,
    show_in_leaderboard = EXCLUDED.show_in_leaderboard
;

INSERT INTO
    app_account.privacy (
        user_id,
        show_profile,
        show_activity,
        show_age,
        show_gender,
        show_in_leaderboard
    )
VALUES
    ('d4e5f6a7-b8c9-4d5e-6f7a-8b9c0d1e2f3a', TRUE, FALSE, FALSE, FALSE, TRUE) ON CONFLICT (user_id)
DO
UPDATE
SET
    show_profile = EXCLUDED.show_profile,
    show_activity = EXCLUDED.show_activity,
    show_age = EXCLUDED.show_age,
    show_gender = EXCLUDED.show_gender,
    show_in_leaderboard = EXCLUDED.show_in_leaderboard
;