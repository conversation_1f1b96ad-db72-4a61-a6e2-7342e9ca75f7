--------------------------------------------------------------------------------
-- Table of Contents
--------------------------------------------------------------------------------
-- 1. Schema
--    1.1. Schema Cleanup
--    1.2. Schema Setup
-- 2. Helper Functions
--    2.1. update_exchange_rates
--    2.2. archive_completed_undisputable_orders
-- 3. Cronjob
--    3.1. update_exchange_rates
--    3.2. archive_completed_undisputable_orders
--------------------------------------------------------------------------------
-- 1. Schema
--------------------------------------------------------------------------------
-- 1.1. Schema Cleanup
--------------------------------------------------------------------------------
DROP SCHEMA IF EXISTS app_private CASCADE
;

--------------------------------------------------------------------------------
-- 1.2. Schema Setup
--------------------------------------------------------------------------------
CREATE SCHEMA IF NOT EXISTS app_private
;

GRANT USAGE ON SCHEMA app_private TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_private TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_private TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_private TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_private
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_private
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_private
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

--------------------------------------------------------------------------------
-- 2. Helper Functions
--------------------------------------------------------------------------------
-- 2.1. update_exchange_rates
--------------------------------------------------------------------------------
CREATE
OR REPLACE FUNCTION app_private.update_exchange_rates () RETURNS VOID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_api_url TEXT;
  v_api_call_secret TEXT;
BEGIN
  -- Get the API_URL secret from the vault
  SELECT decrypted_secret INTO v_api_url
  FROM vault.decrypted_secrets
  WHERE name = 'API_URL';

  -- Get the API_CALL_SECRET secret from the vault
  SELECT decrypted_secret INTO v_api_call_secret
  FROM vault.decrypted_secrets
  WHERE name = 'API_CALL_SECRET';

  -- Make an HTTP GET request to update exchange rates
  PERFORM net.http_get(
    url := v_api_url || '/update-exchange-rates',
    headers := jsonb_build_object(
      'X-API-CALL-SECRET', v_api_call_secret
    ),
    timeout_milliseconds := 30000
  );

  RETURN;
END;
$$
;

--------------------------------------------------------------------------------
-- 2.2. archive_completed_undisputable_orders
--------------------------------------------------------------------------------
CREATE
OR REPLACE FUNCTION app_private.archive_completed_undisputable_orders () RETURNS TRIGGER LANGUAGE plpgsql AS $$
BEGIN
  -- INSERT INTO app_transaction.order_archive SELECT * FROM app_transaction.order WHERE status = 'completed' AND disputable_until < NOW();
  INSERT INTO app_transaction.order_archive
  SELECT *
  FROM app_transaction."order"
  WHERE
    status = 'completed'
    AND disputable_until < NOW();

  -- DELETE FROM app_transaction.order WHERE status = 'completed' AND disputable_until < NOW();
  DELETE FROM app_transaction."order"
  WHERE
    status = 'completed'
    AND disputable_until < NOW();

  RETURN NULL; -- For cron jobs, return NULL
END;
$$
;

--------------------------------------------------------------------------------
-- 3. Cronjob
--------------------------------------------------------------------------------
-- 3.1. update_exchange_rates
--------------------------------------------------------------------------------
SELECT
  cron.schedule (
    'Update Exchange Rates',
    '0 * * * *',
    'SELECT app_private.update_exchange_rates()'
  )
;

--------------------------------------------------------------------------------
-- 3.2. archive_completed_undisputable_orders
--------------------------------------------------------------------------------
SELECT
  cron.schedule (
    'Archive Completed Undisputable Orders',
    '0 0 * * *',
    $$SELECT app_private.archive_completed_undisputable_orders();$$
  )
;