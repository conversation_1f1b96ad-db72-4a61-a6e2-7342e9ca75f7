--------------------------------------------------------------------------------
-- Table of Contents
--------------------------------------------------------------------------------
-- 1. Schema
--    1.1. Schema Cleanup
--    1.2. Schema Setup
-- 2. Types
--    2.1. SERVICE_STATUS
--    2.2. APPLICATION_STATUS
--    2.3. RATING
-- 3. Helper Functions
--    3.1. submit_order
-- 4. Profile
--    4.1. Profile Table Definition
--    4.2. Profile RLS Policies
-- 5. Selected Activity
--    5.1. Selected Activity Table Definition
--    5.2. Selected Activity RLS Policies
--    5.3. Selected Activity Triggers
-- 6. Service
--    6.1. Service Table Definition
--    6.2. Service RLS Policies
--    6.3. Service Triggers
-- 7. Application
--    7.1. Application Table Definition
--    7.2. Application RLS Policies
--    7.3. Application Triggers
-- 8. Service Modifier
--    8.1. Service Modifier Table Definition
--    8.2. Service Modifier RLS Policies
--    8.3. Service Modifier Triggers
-- 9. Review
--    9.1. Review Table Definition
--    9.2. Review RLS Policies
--    9.3. Review Triggers
--------------------------------------------------------------------------------
-- 1. Schema
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 1.1. Schema Cleanup
--------------------------------------------------------------------------------
DROP SCHEMA IF EXISTS app_provider CASCADE
;

--------------------------------------------------------------------------------
-- 1.2. Schema Setup
--------------------------------------------------------------------------------
CREATE SCHEMA IF NOT EXISTS app_provider
;

GRANT USAGE ON SCHEMA app_provider TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL TABLES IN SCHEMA app_provider TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL ROUTINES IN SCHEMA app_provider TO anon,
authenticated,
service_role
;

GRANT ALL ON ALL SEQUENCES IN SCHEMA app_provider TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_provider
GRANT ALL ON TABLES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_provider
GRANT ALL ON ROUTINES TO anon,
authenticated,
service_role
;

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA app_provider
GRANT ALL ON SEQUENCES TO anon,
authenticated,
service_role
;

--------------------------------------------------------------------------------
-- 2. Types
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 2.1. SERVICE_STATUS
--------------------------------------------------------------------------------
CREATE TYPE app_provider.SERVICE_STATUS AS ENUM('draft', 'published')
;

--------------------------------------------------------------------------------
-- 2.2. APPLICATION_STATUS
--------------------------------------------------------------------------------
CREATE TYPE app_provider.APPLICATION_STATUS AS ENUM('pending', 'approved', 'rejected')
;

--------------------------------------------------------------------------------
-- 2.3. RATING
--------------------------------------------------------------------------------
CREATE DOMAIN app_provider.RATING AS INTEGER CHECK (
    VALUE >= 1
    AND VALUE <= 5
)
;

--------------------------------------------------------------------------------
-- 3. Helper Functions
--------------------------------------------------------------------------------
-- 3.1. submit_order
--------------------------------------------------------------------------------
CREATE
OR REPLACE FUNCTION app_provider.submit_order (p_receiver_id UUID, p_soda_amount app_transaction.TOKEN_UNIT) RETURNS UUID LANGUAGE plpgsql SECURITY DEFINER AS $$
DECLARE
  v_sender_id UUID := (SELECT auth.uid());
  v_order_id UUID;
BEGIN
  -- Ensure the sender is not the receiver
  IF v_sender_id = p_receiver_id THEN
    RAISE EXCEPTION 'Sender and receiver cannot be the same.';
  END IF;

  -- Ensure soda_amount is positive
  IF p_soda_amount <= 0 THEN
    RAISE EXCEPTION 'Order soda amount must be greater than 0.';
  END IF;

  -- Insert the new order
  INSERT INTO app_transaction."order" (sender_id, receiver_id, soda_amount)
  VALUES (v_sender_id, p_receiver_id, p_soda_amount)
  RETURNING id INTO v_order_id;

  RETURN v_order_id;
END;
$$
;

--------------------------------------------------------------------------------
-- 4. Profile
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 4.1. Profile Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
    app_provider.profile (
        user_id UUID PRIMARY KEY REFERENCES auth.users (id) ON DELETE CASCADE,
        slug TEXT UNIQUE NOT NULL CHECK (
            slug ~ '^[a-z0-9]+(?:-[a-z0-9]+)*$'
            AND LENGTH(slug) <= 50
        ) -- slug must be URL-friendly (lowercase letters, numbers, hyphens, no leading/trailing hyphens) and have a maximum length of 50 characters
    )
;

--------------------------------------------------------------------------------
-- 4.2. Profile RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_provider.profile ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "profile_select_all" ON app_provider.profile FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "profile_insert_admin" ON app_provider.profile FOR INSERT
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "profile_update_admin" ON app_provider.profile FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

CREATE POLICY "profile_delete_admin" ON app_provider.profile FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 5. Selected Activity
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 5.1. Selected Activity Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
    app_provider.selected_activity (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
        user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
        activity_id UUID NOT NULL REFERENCES app_activity.activity (id) ON DELETE CASCADE,
        description JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE (user_id, activity_id)
    )
;

--------------------------------------------------------------------------------
-- 5.2. Selected Activity RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_provider.selected_activity ENABLE ROW LEVEL SECURITY
;

-- Allow users to select their own selected activities and admins to select all
CREATE POLICY "selected_activity_select_own_or_admin" ON app_provider.selected_activity FOR
SELECT
    USING (
        (
            SELECT
                auth.uid ()
        ) = user_id
        OR (
            SELECT
                app_role.is_admin ()
        )
    )
;

-- Allow authenticated users to insert selected activities
CREATE POLICY "selected_activity_insert_own_or_admin" ON app_provider.selected_activity FOR INSERT
WITH
    CHECK (
        (
            SELECT
                auth.uid ()
        ) = user_id
        OR (
            SELECT
                app_role.is_admin ()
        )
    )
;

-- Allow users to delete their own selected activities and admins to delete all
CREATE POLICY "selected_activity_delete_own_or_admin" ON app_provider.selected_activity FOR DELETE USING (
    (
        SELECT
            auth.uid ()
    ) = user_id
    OR (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 5.3. Selected Activity Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER selected_activity_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_provider.selected_activity FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('description')
;

--------------------------------------------------------------------------------
-- 6. Service
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 6.1. Service Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
    app_provider.service (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
        user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
        NAME JSONB NOT NULL,
        description JSONB,
        selected_activity_id UUID REFERENCES app_provider.selected_activity (id) ON DELETE CASCADE,
        status app_provider.SERVICE_STATUS NOT NULL DEFAULT 'draft',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        soda_amount app_transaction.TOKEN_UNIT NOT NULL DEFAULT 0
    )
;

--------------------------------------------------------------------------------
-- 6.2. Service RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_provider.service ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "service_select_all" ON app_provider.service FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "service_insert_own_or_admin" ON app_provider.service FOR INSERT
WITH
    CHECK (
        (
            SELECT
                auth.uid ()
        ) = user_id
        OR (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "service_update_own_or_admin" ON app_provider.service FOR
UPDATE USING (
    (
        SELECT
            auth.uid ()
    ) = user_id
    OR (
        SELECT
            app_role.is_admin ()
    )
)
WITH
    CHECK (
        (
            SELECT
                auth.uid ()
        ) = user_id
        OR (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "service_delete_own_or_admin" ON app_provider.service FOR DELETE USING (
    (
        SELECT
            auth.uid ()
    ) = user_id
    OR (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 6.3. Service Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER service_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_provider.service FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('name', 'description')
;

--------------------------------------------------------------------------------
-- 7. Application
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 7.1. Application Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
    app_provider.application (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
        user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
        application_status app_provider.APPLICATION_STATUS NOT NULL DEFAULT 'pending',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
;

--------------------------------------------------------------------------------
-- 7.2. Application RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_provider.application ENABLE ROW LEVEL SECURITY
;

-- Allow users to select their own applications and admins to select all
CREATE POLICY "application_select_own_or_admin" ON app_provider.application FOR
SELECT
    USING (
        (
            SELECT
                auth.uid ()
        ) = user_id
        OR (
            SELECT
                app_role.is_admin ()
        )
    )
;

-- Allow authenticated users to insert applications
CREATE POLICY "application_insert_authenticated" ON app_provider.application FOR INSERT
WITH
    CHECK (
        (
            SELECT
                auth.uid ()
        ) IS NOT NULL
    )
;

-- Allow admins to update applications (status, notes)
CREATE POLICY "application_update_admin" ON app_provider.application FOR
UPDATE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
WITH
    CHECK (
        (
            SELECT
                app_role.is_admin ()
        )
    )
;

-- Allow admins to delete applications
CREATE POLICY "application_delete_admin" ON app_provider.application FOR DELETE USING (
    (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 7.3. Application Triggers
--------------------------------------------------------------------------------
-- Automatically update updated_at timestamp
CREATE TRIGGER application_set_updated_at BEFORE
UPDATE ON app_provider.application FOR EACH ROW
EXECUTE FUNCTION public.set_updated_at ()
;

--------------------------------------------------------------------------------
-- 8. Service Modifier
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 8.1. Service Modifier Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
    app_provider.service_modifier (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
        selected_activity_id UUID NOT NULL REFERENCES app_provider.selected_activity (id) ON DELETE CASCADE,
        NAME JSONB NOT NULL,
        description JSONB,
        soda_amount app_transaction.TOKEN_UNIT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
;

--------------------------------------------------------------------------------
-- 8.2. Service Modifier RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_provider.service_modifier ENABLE ROW LEVEL SECURITY
;

CREATE POLICY "service_modifier_select_all" ON app_provider.service_modifier FOR
SELECT
    USING (TRUE)
;

CREATE POLICY "service_modifier_insert_own_or_admin" ON app_provider.service_modifier FOR INSERT
WITH
    CHECK (
        (
            SELECT
                auth.uid ()
        ) = (
            SELECT
                user_id
            FROM
                app_provider.selected_activity
            WHERE
                id = selected_activity_id
        )
        OR (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "service_modifier_update_own_or_admin" ON app_provider.service_modifier FOR
UPDATE USING (
    (
        SELECT
            auth.uid ()
    ) = (
        SELECT
            user_id
        FROM
            app_provider.selected_activity
        WHERE
            id = selected_activity_id
    )
    OR (
        SELECT
            app_role.is_admin ()
    )
)
WITH
    CHECK (
        (
            SELECT
                auth.uid ()
        ) = (
            SELECT
                user_id
            FROM
                app_provider.selected_activity
            WHERE
                id = selected_activity_id
        )
        OR (
            SELECT
                app_role.is_admin ()
        )
    )
;

CREATE POLICY "service_modifier_delete_own_or_admin" ON app_provider.service_modifier FOR DELETE USING (
    (
        SELECT
            auth.uid ()
    ) = (
        SELECT
            user_id
        FROM
            app_provider.selected_activity
        WHERE
            id = selected_activity_id
    )
    OR (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 8.3. Service Modifier Triggers
--------------------------------------------------------------------------------
CREATE TRIGGER service_modifier_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_provider.service_modifier FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('name', 'description')
;

--------------------------------------------------------------------------------
-- 9. Review
--------------------------------------------------------------------------------
--------------------------------------------------------------------------------
-- 9.1. Review Table Definition
--------------------------------------------------------------------------------
CREATE TABLE
    app_provider.review (
        order_id UUID PRIMARY KEY REFERENCES app_transaction.order (id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES auth.users (id) ON DELETE CASCADE,
        rating app_provider.RATING NOT NULL,
        COMMENT JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    )
;

--------------------------------------------------------------------------------
-- 9.2. Review RLS Policies
--------------------------------------------------------------------------------
ALTER TABLE app_provider.review ENABLE ROW LEVEL SECURITY
;

-- Allow all users to select reviews
CREATE POLICY "review_select_all" ON app_provider.review FOR
SELECT
    USING (TRUE)
;

-- Allow authenticated users to insert their own reviews
CREATE POLICY "review_insert_own" ON app_provider.review FOR INSERT
WITH
    CHECK (
        (
            SELECT
                auth.uid ()
        ) = user_id
    )
;

-- Allow users to update their own reviews or admins to update all
CREATE POLICY "review_update_own_or_admin" ON app_provider.review FOR
UPDATE USING (
    (
        SELECT
            auth.uid ()
    ) = user_id
    OR (
        SELECT
            app_role.is_admin ()
    )
)
WITH
    CHECK (
        (
            SELECT
                auth.uid ()
        ) = user_id
        OR (
            SELECT
                app_role.is_admin ()
        )
    )
;

-- Allow users to delete their own reviews or admins to delete all
CREATE POLICY "review_delete_own_or_admin" ON app_provider.review FOR DELETE USING (
    (
        SELECT
            auth.uid ()
    ) = user_id
    OR (
        SELECT
            app_role.is_admin ()
    )
)
;

--------------------------------------------------------------------------------
-- 9.3. Review Triggers
--------------------------------------------------------------------------------
-- Automatically update updated_at timestamp
CREATE TRIGGER review_set_updated_at BEFORE
UPDATE ON app_provider.review FOR EACH ROW
EXECUTE FUNCTION public.set_updated_at ()
;

-- Validate locale columns for the 'comment' field
CREATE TRIGGER review_validate_locale_columns BEFORE INSERT
OR
UPDATE ON app_provider.review FOR EACH ROW
EXECUTE FUNCTION public.validate_locale_columns ('comment')
;